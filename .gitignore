# NodeJS Files
node_modules
npm-debug.log

# Cache Files
system/cache

# OSX Files
*.DS_Config
*.DS_Store

# IDEs Files
*.sublime-project
*.sublime-workspace 


# Local Terraform directories
**/.terraform/*

# Terraform state files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Terraform variable files that may contain sensitive data
*.tfvars
*.tfvars.json

# Terraform override files
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Plan output files
*.tfplan

# Terraform CLI configuration files
.terraformrc
terraform.rc

# Local settings
.terraform/environment

# Module and provider packages (for Terraform 0.13 and later)
**/.terraform.lock.hcl

# Lock file for pre-0.14 Terraform
**/terraform.tfstate.lock.info

# MacOS specific files
.DS_Store

# IDE/editor swap files
*.swp
