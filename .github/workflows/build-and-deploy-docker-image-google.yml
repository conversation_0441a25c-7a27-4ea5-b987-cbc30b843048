name: <PERSON>uild and Push Docker Image to Google Artifact Registry

on:
  push:
    branches:
      - main

env:
  IMAGE_URL: ${{ vars.GCP_REPOSITORY_URL }}/${{vars.IMAGE_NAME}}:latest
  REGION: europe-west2

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authentication
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Build and Push Image
        run: |
          docker build --platform linux/amd64 -f ci/Dockerfile -t ${{ env.IMAGE_URL }} .
          docker push ${{ env.IMAGE_URL }}
