// Node Modules
const fs = require("fs");
const ejs = require("ejs");
const glob = require("glob");
const https = require("https");
const fse = require("fs-extra");
const sitePath = process.cwd();
const site_config = require(`${sitePath}/config`);

// Useful Functions
module.exports = {
	siteSettings: "",

	pages_modules: {},

	getPagePath: function (story, log) {
		let url = "";
		if (story.slug && story.slug !== "homepage") {
			url = `/${
				story.url.split("/").slice(0, -2).join("/")
					? `${story.url.split("/").slice(0, -2).join("/")}/`
					: ""
			}${story.slug.substring(0, 30)}-${story.id}/`;
		} else if (story.slug && story.slug === "homepage") {
			url = `/`;
		} else {
			url = story.url;
		}
		return url.replace(/\/\//g, "/");
	},

	// Return site settings
	getSiteSettings: function () {
		var self = this;

		if (self.siteSettings == "") {
			self.siteSettings =
				module.parent.plugins.readJSONFile("data/settings.json");
		}
		return self.siteSettings;
	},

	// Return all the pages of the website
	// (not parsed with EJS)
	getRawPages: function () {
		var pages = [];
		var files = glob.sync(`${sitePath}/pages/**/*.html`);
		var length = files.length;
		for (var i = 0; i < length; i++) {
			var path = files[i];
			var fileContent = fs.readFileSync(path, "utf-8");
			var matches = fileContent.match(/<config>[^]+<\/config>/g);
			var content = fileContent.replace(matches[0], "");
			var config = eval(
				JSON.parse(
					"{" +
						matches[0].replace("<config>", "").replace("</config>", "") +
						"}"
				)
			);
			config.path = path;
			config.content = content;
			if (config.data === undefined) config.data = {};
			if (config.id && config.fetch_timestamp && config.slug != "homepage") {
				var already_existing = pages.find((entry) => entry.id == config.id);

				if (
					already_existing &&
					already_existing.fetch_timestamp < config.fetch_timestamp
				) {
					pages = pages.filter((entry) => entry.id != config.id);
				} else if (already_existing) {
					continue;
				}
			}
			pages.push(config);
		}
		return pages;
	},

	// Return specific page of the website (based on file path)
	getRawPage: function (filePath) {
		var fileContent = fs.readFileSync(filePath, "utf-8");
		var matches = fileContent.match(/<config>[^]+<\/config>/g);
		var content = fileContent.replace(matches[0], "");
		var config = eval(
			JSON.parse(
				"{" + matches[0].replace("<config>", "").replace("</config>", "") + "}"
			)
		);
		config.content = content;
		if (config.data === undefined) config.data = {};
		return config;
	},

	// Storing pages modules in an array from the current sitemap
	// to use in sitemaprecursive when rebuilding the sitemap
	// on fetch --entry-id=  for fetching a single entry
	getPagesModules: function () {
		var self = this;
		var sitemap = fse.readJsonSync(`${sitePath}/data/sitemap.json`);
		function getPagesModuleSubroutine(entries) {
			if (entries.length) {
				entries.forEach((entry) => {
					self.pages_modules[entry.id] = entry.module;
					if (entry.hasOwnProperty("sub_stories")) {
						getPagesModuleSubroutine(entry.sub_stories);
					}
				});
			}
		}
		getPagesModuleSubroutine(sitemap);
	},

	// Sitemap recursive generator
	sitemapRecursive: function (parent_id, links, stories) {
		var self = this;
		var filtered_links = links.filter((link) => link.parent_id == parent_id);
		var tree = [];

		if (filtered_links.length) {
			filtered_links.forEach(function (link, index) {
				var new_element = {};
				var current_element = {};
				var url = "";
				if (link.is_folder) {
					// If it's a folder the entry is listed anyway BUT
					current_element = link;
					starting_page = links.find(
						(sub_link) => sub_link.parent_id == link.id && sub_link.is_startpage
					);
					// BUT if there's a starting page, a starting page property is added to the entry
					if (starting_page) {
						current_element.starting_page = {
							id: starting_page.id,
							title: starting_page.name,
							url: `/${starting_page.slug.replace(/\/$/, "")}/`,
							hidden: false,
							module: stories
								? stories.find((story) => story.id == starting_page.id).data
										.component
								: self.pages_modules[starting_page.id],
						};
					}
					if (
						links.filter((sub_link) => sub_link.parent_id == link.id).length
					) {
						new_element.sub_stories = self.sitemapRecursive(
							link.id,
							links,
							stories
						);
					}
				} else {
					current_element = link;
				}
				if (current_element.slug == "homepage") {
					url = "/";
				} else {
					url = `/${current_element.slug.replace(/\/$/, "")}/`;
				}
				var new_element_data = {
					id: current_element.id,
					title: current_element.name,
					url: url,
					hidden: false,
				};

				// Getting the module
				if (stories) {
					// From the stories object
					if (stories.find((story) => story.id == current_element.id)) {
						new_element_data.module = stories.find(
							(story) => story.id == current_element.id
						).data.component;
					} else {
						new_element_data.module = "Folder";
					}
				} else {
					// From the previous sitemap
					new_element_data.module = self.pages_modules[current_element.id];
				}

				// If it's a folder
				if (new_element_data.module === "Folder") {
					new_element_data.starting_page = current_element.starting_page;
				}

				// If it's a start page
				if (current_element.is_startpage) {
					new_element_data.is_startpage = true;
				}

				new_element = Object.assign(new_element, new_element_data);
				tree.push(new_element);
			});
		}

		return tree;
	},

	// Lightweight & Simple request method
	request: function (url) {
		return new Promise((resolve, reject) => {
			https
				.get(url, (resp) => {
					let data = "";
					resp.on("data", (chunk) => {
						data += chunk;
					});
					resp.on("end", () => {
						resolve(data);
					});
				})
				.on("error", (err) => {
					reject(err.message);
				});
		});
	},

	// Create a page
	createPage: function (config, content, path) {
		var page_content = JSON.stringify(config, null, "\t");

		// Url replacement of assets in case it's set
		if (site_config.assets_url_masking && site_config.force_imgix) {
			page_content = page_content.replace(
				/s3\.amazonaws\.com\/a\.storyblok\.com|a\.storyblok\.com/g,
				site_config.assets_url_masking
			);
		}

		var formattedConfig = `<config>${page_content}</config>`
			.replace("<config>{", "<config>")
			.replace("}</config>", "</config>");
		var filePath = `${sitePath}/pages/${path}`.replace(/\/\//g, "/");
		fse.outputFileSync(filePath, formattedConfig + content);
	},

	// Render a specific page
	renderPage: function (path, page, resolve, reject) {
		var self = this;

		// Memorize current page
		module.parent.page = page;

		try {
			// Render page.content if not empty
			if (page.content) {
				page.content = ejs.render(page.content, {
					site: {
						config: module.parent.config,
						pages: module.parent.pages,
						settings: self.getSiteSettings(),
					},
					page: page,
					plugins: module.parent.plugins,
					environment: module.parent.environment,
				});
			}
		} catch (error) {
			console.log(error);
			if (typeof reject == "function") {
				reject(error);
			}
		}

		// Should we render the page or the layout?
		var toRender = page.content;
		if (page.layout !== undefined) {
			var layoutPath = `layouts/${page.layout}.html`;
			if (module.parent.templates[layoutPath] === undefined) {
				module.parent.templates[layoutPath] = fs.readFileSync(
					layoutPath,
					"utf-8"
				);
			}
			toRender = module.parent.templates[layoutPath];
		}

		try {
			// Parse the page with EJS
			var render = ejs.render(toRender, {
				site: {
					config: module.parent.config,
					pages: module.parent.pages,
					settings: self.getSiteSettings(),
				},
				page: page,
				plugins: module.parent.plugins,
				environment: module.parent.environment,
			});

			// Config find / replace
			if (module.parent.config.replace) {
				module.parent.config.replace.forEach((line) => {
					if (
						typeof line[0] === "string" &&
						line[0].indexOf("a.storyblok.com/f") >= 0 &&
						!site_config.force_imgix
					)
						return;
					let replace_this = line[0];
					let by_this = line[1];
					let regex = typeof replace_this === "object";
					render = regex
						? render.replace(replace_this, by_this)
						: render.replace(new RegExp(replace_this, "g"), by_this);
				});
			}

			// Write the file in the build directory
			fse.outputFile(path, render);
			if (typeof resolve == "function") {
				resolve();
			}
		} catch (error) {
			if (typeof reject == "function") {
				reject(error);
			}
		}
	},
};
