// Node Modules
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const sitePath = process.cwd();

/**
 * Script Hooks System
 * Executes custom scripts defined in config.js at specific lifecycle points
 */
class ScriptHooks {
    constructor() {
        this.config = null;
        this.loadConfig();
    }

    /**
     * Load configuration from config.js
     */
    loadConfig() {
        try {
            const configPath = path.join(sitePath, 'config.js');
            if (fs.existsSync(configPath)) {
                // Clear require cache to get fresh config
                delete require.cache[require.resolve(configPath)];
                this.config = require(configPath);
            }
        } catch (error) {
            console.warn('Warning: Could not load config.js for script hooks:', error.message);
            this.config = {};
        }
    }

    /**
     * Execute a script hook if it exists in config
     * @param {string} hookName - Name of the hook (e.g., 'prefetch', 'postbuild')
     * @param {object} options - Options for execution
     */
    async executeHook(hookName, options = {}) {
        const { silent = false, cwd = sitePath } = options;
        
        if (!this.config || !this.config.scriptHooks || !this.config.scriptHooks[hookName]) {
            return; // No hook defined, silently continue
        }

        const script = this.config.scriptHooks[hookName];
        
        if (!script || typeof script !== 'string') {
            return; // Invalid script definition
        }

        if (!silent) {
            console.log(`\n🔧 Executing ${hookName} hook: ${script}`);
        }

        try {
            const output = execSync(script, {
                cwd: cwd,
                stdio: silent ? 'pipe' : 'inherit',
                encoding: 'utf8'
            });
            
            if (!silent && output) {
                console.log(output);
            }
            
            if (!silent) {
                console.log(`✅ ${hookName} hook completed successfully`);
            }
        } catch (error) {
            console.error(`❌ Error executing ${hookName} hook:`, error.message);
            if (error.stdout) {
                console.error('STDOUT:', error.stdout);
            }
            if (error.stderr) {
                console.error('STDERR:', error.stderr);
            }
            throw error; // Re-throw to allow calling code to handle
        }
    }

    /**
     * Execute prefetch hook
     */
    async prefetch(options = {}) {
        return this.executeHook('prefetch', options);
    }

    /**
     * Execute postfetch hook
     */
    async postfetch(options = {}) {
        return this.executeHook('postfetch', options);
    }

    /**
     * Execute prebuild hook
     */
    async prebuild(options = {}) {
        return this.executeHook('prebuild', options);
    }

    /**
     * Execute postbuild hook
     */
    async postbuild(options = {}) {
        return this.executeHook('postbuild', options);
    }

    /**
     * Execute prewatch hook
     */
    async prewatch(options = {}) {
        return this.executeHook('prewatch', options);
    }

    /**
     * Execute preassets hook
     */
    async preassets(options = {}) {
        return this.executeHook('preassets', options);
    }

    /**
     * Execute postassets hook
     */
    async postassets(options = {}) {
        return this.executeHook('postassets', options);
    }

    /**
     * Check if a specific hook is defined
     * @param {string} hookName - Name of the hook to check
     * @returns {boolean}
     */
    hasHook(hookName) {
        return !!(this.config && 
                 this.config.scriptHooks && 
                 this.config.scriptHooks[hookName] && 
                 typeof this.config.scriptHooks[hookName] === 'string');
    }

    /**
     * Get all defined hooks
     * @returns {object} Object with hook names as keys and scripts as values
     */
    getHooks() {
        if (!this.config || !this.config.scriptHooks) {
            return {};
        }
        return { ...this.config.scriptHooks };
    }
}

// Export singleton instance
module.exports = new ScriptHooks();
