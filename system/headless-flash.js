// Node Modules
const utils = require("./utils");
const fs = require("fs");
const ejs = require("ejs");
const fse = require("fs-extra");
const path = require("path");
const sitePath = process.cwd();
const source = path.dirname(fs.realpathSync(__filename));

// Global Variables
// (For EJS & Plugins)
module.config = require(`${sitePath}/config`);
module.pages = utils.getRawPages();
module.page = {};
module.templates = {};

// Get all the plugins in the
// system/plugins directory.
module.plugins = {};
var files = fs.readdirSync(path.join(__dirname, "plugins"));
if (fs.existsSync(`${sitePath}/plugins`)) {
	files = files.concat(fs.readdirSync(path.join(sitePath, "plugins")));
}
var filesLength = files.length;
for (var i = 0; i < filesLength; i++) {
	var fileName = files[i];
	var fileNameWithoutExt = path.parse(fileName).name;
	var systemPluginPath = `${source}/plugins/${fileName}`;
	var sitePluginPath = `${sitePath}/plugins/${fileName}`;
	module.plugins[fileNameWithoutExt] = require(fs.existsSync(systemPluginPath)
		? systemPluginPath
		: sitePluginPath);
}

// Remember which files are getting included (for optimized compilation time)
module.includes = {};

// Environment
module.environment = "local";

// Make sure that the cache folder is created

fse.ensureDirSync(`${sitePath}/data`);

const INCLUDES_CACHE_FILE = `${sitePath}/data/flash-build-includes.json`;

/**
 *          /
 *        //
 *      / /
 *    /  /____
 *  /____    /
 *      /  /
 *     / /
 *    //
 *   /
 *
 * Headless Flash
 * @version 1.0.0
 * <AUTHOR> Corbalan <<EMAIL>>
 */
class HeadlessFlash {
	/**
	 * Build the website
	 *
	 * @access static
	 * @returns void
	 */
	static build(environment) {
		// Updates environment
		module.environment = environment;

		// Update pages
		module.pages = utils.getRawPages();

		if (!module.pages || !module.pages.length) {
			throw new Error("No pages found");
		}

		// Empty the build folder
		fse.emptyDirSync(module.config.build.folder);

		// Go each page and move them at the correct location
		var length = module.pages.length;
		var pages_to_build = [];
		var exclude_modules = module.config.modules_without_detail;
		process.stdout.write(`\nBuilding ${length} pages \n`);
		for (var i = 0; i < length; i++) {
			// Update the current page global variable
			var page = module.pages[i];
			if (!exclude_modules.includes(page.data.component)) {
				var new_page = new Promise(function (resolve, reject) {
					// Get file informations
					var fileUrl = page.url;
					var fileExtension =
						fileUrl.indexOf(".") >= 0 && fileUrl.split(".").pop();
					var filePath = fileExtension
						? module.config.build.folder + fileUrl
						: module.config.build.folder + fileUrl + "/index.html";

					// Create the page
					utils.renderPage(filePath, page, resolve, reject);
				}).catch((error) =>
					process.stderr.write(
						`============ ### error in ${page.slug} ============\n\n${error}\n\n============ ### End of Error in ${page.slug} ============\n\n\n`
					)
				);
				pages_to_build.push(new_page);
			}
		}

		Promise.all(pages_to_build).then(function (results) {
			// Write the included files cache
			fse.writeJsonSync(INCLUDES_CACHE_FILE, module.includes);

			HeadlessFlash.fetchAdditionalFiles();

			results.forEach(function (result) {
				if (result) {
					process.stderr.write(result.toString());
				}
			});
		});
	}

	/**
	 * Build a specific page
	 *
	 * @access static
	 * @returns void
	 */
	static buildPage(filePath, environment) {
		// Updates environment
		module.environment = environment;

		// Mah :(
		if (!fs.existsSync(filePath)) {
			process.stderr.write(filePath + ` not found\n`);
			return;
		}

		// Is it a layout?
		if (filePath.indexOf("layouts/") >= 0) return HeadlessFlash.build();

		// Pages to recompile
		var pages = [];

		// Read the includes JSON cache to find if something match in the values or the key
		if (fs.existsSync(INCLUDES_CACHE_FILE)) {
			var includes = fse.readJsonSync(INCLUDES_CACHE_FILE);
			for (var pagePath in includes) {
				if (
					includes[pagePath].indexOf(filePath) >= 0 &&
					pages.indexOf(pagePath) < 0
				)
					pages.push(pagePath);
			}
		}

		// No includes, just normal page
		if (!pages.length && filePath.startsWith("pages/")) pages.push(filePath);

		// Recompile all the necessary pages only
		var pages_to_build = [];
		for (var i = 0; i < pages.length; i++) {
			var page = utils.getRawPage(pages[i]);
			var new_page = new Promise(function (resolve, reject) {
				// Get file informations
				var fileUrl = page.url;
				var fileExtension =
					fileUrl.indexOf(".") >= 0 && fileUrl.split(".").pop();
				var filePath = fileExtension
					? module.config.build.folder + fileUrl
					: module.config.build.folder + fileUrl + "/index.html";

				// Build the page
				utils.renderPage(filePath, page, resolve, reject);
				console.log(page.url + " Recompiled!");
			}).catch((error) =>
				process.stderr.write(
					`============ ### Error in ${page.slug} ============\n\n${error}\n\n============ ### End of Error in ${page.slug} ============\n\n\n`
				)
			);

			pages_to_build.push(new_page);
		}
	}

	/**
	 * Build entry live
	 * @param  {object} page Page to rebuild
	 * @return {[type]}      [description]
	 */
	static buildEntryLive(page) {
		// Updates environment
		module.environment = "dev";

		module.page = page;

		if (page.layout !== undefined) {
			var layoutPath = `layouts/${page.layout}.html`;
			if (module.templates[layoutPath] === undefined) {
				module.templates[layoutPath] = fs.readFileSync(layoutPath, "utf-8");
			}
			var toRender = module.templates[layoutPath];
		}
		var render = ejs.render(toRender, {
			site: {
				config: module.config,
				pages: module.pages,
				settings: utils.getSiteSettings(),
			},
			page: Object.assign({}, page),
			plugins: module.plugins,
			environment: module.environment,
		});
		return render;
	}

	/**
	 * Build a specific page
	 *
	 * @access static
	 * @returns void
	 */
	static buildEntry(entry_id, environment) {
		// Updates environment
		module.environment = environment;

		var sitemap = fse.readJsonSync(`${sitePath}/data/sitemap.json`);

		// Is it a layout?
		if (!sitemap) {
			process.stderr.write("error: sitemap not found");
			return;
		}

		module.pages = utils.getRawPages();

		// Pages to recompile
		var pages = [];
		// Fetches the page by id
		function checkSubpagesById(folder_pages) {
			if (folder_pages.length) {
				folder_pages.forEach(function (page, index) {
					if (page.hasOwnProperty("sub_stories")) {
						checkSubpagesById(page.sub_stories);
					} else {
						if (page.id == entry_id) {
							pages.push(page);
						}
					}
				});
			}
		}
		if (sitemap.length) {
			checkSubpagesById(sitemap);
		}

		// Fetches all the pages by folder
		function checkSubpagesByFolder(folder_pages, url) {
			if (folder_pages.length) {
				folder_pages.forEach(function (page, index) {
					if (page.hasOwnProperty("sub_stories")) {
						checkSubpagesByFolder(page.sub_stories, url);
					} else {
						if (
							page.url.startsWith(url) &&
							["Page", "Standard Page Module"].includes(page.module)
						) {
							pages.push(module.pages.find((entry) => entry.id == page.id));
						}
					}
				});
			}
		}

		// In case no pages with that id are found
		if (pages.length) {
			// If the entry is found, search for all the other entries
			// in the same folder (starting from the root)
			pages.forEach(function (page, index) {
				var url_parts = page.url.split("/");
				if (url_parts.length > 2) {
					checkSubpagesByFolder(sitemap, `/${url_parts[1]}`);
				}
			});
		}

		var current_page = module.pages.find((entry) => entry.id == entry_id);

		if (current_page) {
			pages.push(current_page);
		}

		if (!pages.length) {
			process.stderr.write("error: Entry not found");
			return;
		}

		pages.forEach(function (page, index) {
			let filePath = `pages/storyblok${utils.getPagePath(page)}index.html`;
			// Building just in case the path exists
			if (fs.existsSync(filePath)) {
				HeadlessFlash.buildPage(filePath, environment);
			}
		});
	}

	/**
	 * Add/Remove necessary folders/files
	 * from the config.js file.
	 *
	 * @access static
	 * @returns void
	 */
	static fetchAdditionalFiles() {
		// Go each folders/files to join to the build
		// and copy them into the build directory
		var includes = module.config.build.include;
		var includesLength = includes.length;
		for (var i = 0; i < includesLength; i++) {
			var toInclude = includes[i];
			fse.copySync(toInclude, `${module.config.build.folder}/${toInclude}`);
		}

		// Now remove the unwanted folders/files
		var excludes = module.config.build.exclude;
		var excludesLength = excludes.length;
		for (var i = 0; i < excludesLength; i++) {
			var toExclude = excludes[i].replace(/\.\.\//g, ""); // Security purposes
			fse.removeSync(`${module.config.build.folder}/${toExclude}`);
		}
	}
}

module.exports = HeadlessFlash;
