/**
 * Cut a string
 * @param  {[string]} text                      The string you need to cut
 * @param  {[int]} options.length               The number of characters you want to keep
 * @param  {[bool]} options.preserve_words      Set true if you don't want to cut words
 * @return {[string]}                           Your cut     
 *
 *
 * Example
 * cutText('This string will be cut', {length: 14, preserve_words: true})
 * 
 * will return: This string
 */
var cutText = function(text, options){
	// Default options
	options = Object.assign({
		preserve_words: false,
		length: 90
	}, options);

    // If no text, just return
    if(!text) {
    	return '';
    }

    // Trimming whitespaces 
    text = text.trim();

    if(text.length > options.length) {
        // If the text is longer than the limit
    	if(options.preserve_words) {
            // If we have to preserve words
    		var trimmedString = text.substr(0, options.length);
    		return `${trimmedString.substr(0, Math.min(trimmedString.length, trimmedString.lastIndexOf(" ")))}...`;
    	} else {
            // Just regular cut
    		return `${text.substr(0, options.length)}...`;
    	}
    } else {
        // If it's shorter just return the whole string
    	return text;
    }
}
module.exports = cutText;
