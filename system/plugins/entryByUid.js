// Cached URLs
var cachedUrls = {};

// Entry By Uid Plugin
// Usage Example:
// <%= plugins.entryByUid(uid) %>
var entryByUid = function(uid) {
    var pages = module.parent.pages;
    var length = pages.length;
    var page_output = {};
    if(cachedUrls[uid] === undefined) {
        for(var i = 0; i < length; i++) {
            var page = pages[i];
            if(page.uuid !== undefined && page.uuid == uid) {
                page_output = page;
                cachedUrls[uid] = page;
                break;
            }
        }
    } else page_output = cachedUrls[uid];
    return page_output;
};
module.exports = entryByUid;