// Node Modules
const fs = require('fs');

// Already checked existing files
var exists = {};

// Blocks Plugin
// Usage Example:
// <%= plugins.blocks(page.data.body) %>
// Will return: test-here
var blocks = function(data, options) {
    var render = '';
    if(options && options.hasOwnProperty('pageUid') && module.parent.plugins.entryByUid(options.hasOwnProperty('pageUid')) && options.container != '') {
        var entry = module.parent.plugins.entryByUid(options.pageUid);  
        if(entry && entry.hasOwnProperty('data') && entry.data.hasOwnProperty(options.container)) {
            data = entry.data[options.container];
        } 
    }

    for(var index in data) {

        // Get block informations
        var block = data[index];
        var blockName = block.component;
        var blockSlug = module.parent.plugins.slugify(blockName).replace(/-/g, '_');

        // Get the block type
        var matches = blockName.match(/\[(.*?)\]/);
        var blockType = matches && matches[1].toLowerCase();
        if(!blockType) continue; // Not a correct block!

        // Get block path
        var blockPath = `blocks/${blockType}/${blockSlug.replace(`${blockType}_`, '')}/view.html`;

        // Check if the block exists
        if(exists[blockPath] === undefined) exists[blockPath] = fs.existsSync(blockPath);

        // Include the file
        if(exists[blockPath]) render += module.parent.plugins.include(blockPath, { block: block });
        
    }
    return render;
};
module.exports = blocks;