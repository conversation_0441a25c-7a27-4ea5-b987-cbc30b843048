// Cached URLs
var relationship = function(field, iterator  = () => {}, noResults  = () => {}) {
    var output = '';
    var ids = [];

    if(field && field.hasOwnProperty('ids')) {
        // Old relationship field
        ids = field.ids;
    } else if (field && Array.isArray(field) && field.length) {
        // Storyblok native field
        ids = field;
    } else if (field && typeof field === 'string') {
        ids = [field];
    }

    if(ids.length) {
        ids.forEach( (id, index) => {
            var entry = module.parent.plugins.getEntries({'uuid': id});
            if(entry) {
                output += iterator(entry, index);
            }
        });
    } else {
        noResults();
    }
    return output;
}
module.exports = relationship;