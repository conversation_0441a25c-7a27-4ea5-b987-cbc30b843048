// Cached JSON Files
var cachedValues = {};

// Get Categories Plugin
// Usage Example:
// <%= plugins.getFieldValues('Projects Module', 'location') %>
var getFieldValues = function(component, field) {
    component = typeof component === 'string' ? [component] : component;
    var cache_name = component.join() + field;
    if(cachedValues[cache_name] === undefined) {
        var pages = module.parent.pages.filter(page => component.includes(page.data.component));
        var values = [];
        for(var i = 0; i < pages.length; i++) {
            var page = pages[i];
            try{
                var value = eval(`page.data.${field}`);
                if(value) {
                    if(typeof value === 'string') {
                        value = [value];
                    };
                    value.forEach(function(category){
                        if(values.indexOf(category) < 0) {
                            values.push(category);
                        }
                    });
                }
            } catch(e){}
        }
        cachedValues[cache_name] = values;
    }
    return cachedValues[cache_name];
};
module.exports = getFieldValues;