// Capture Plugin
// Usage Example:
// <% plugins.capture('mycapture', function() { %>
//     <div class="crazy-capture">
//         Hello there girls :)
//     </div>
// <% }) %>
// <%= plugins.capture('mycapture') %>
var capture = function(name, template) {
	if(!template && module.parent.page[name] !== undefined) return module.parent.page[name]();
    else module.parent.page[name] = template;
};
module.exports = capture;