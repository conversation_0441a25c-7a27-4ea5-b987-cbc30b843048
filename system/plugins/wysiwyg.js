// Script that outputs the TD Wysiwyg editor content
// Example
// 
// Wrapping the text
// <%- plugins.wysiwyg(block.description, '<div class="block__description">{text}</div>'); %> 
// 
// Not wrapping it
// <%- plugins.wysiwyg(block.description); %>

var wysiwyg = function(field, template) {
    if(!field || !field.value) {
        return '';
    }
    // If a template is set
    if(template) {
    	return template.replace('{text}', field.value);
    }
    return field.value;
}
module.exports = wysiwyg;