// Node Modules
const moment = require('moment');
// Cache object
var dates_cache = {};
// For info about dates formats check https://momentjs.com/docs/#/parsing/string-format/

// Blocks Plugin
// Usage Example:
// <%= plugins.formatDate('2018-02-01', 'DD/MM/YYYY') %>
// Will return: 01/02/2018
var formatDate = function(data, format) {
    // If a cached formatted date exists, return this one
    if(dates_cache[data] && dates_cache[data][format]) {
        return dates_cache[data][format];
    } else {
        var dateToFormat = moment(data);
        if(!dates_cache[data]) {
            dates_cache[data] = {};
        }
        dates_cache[data][format] = dateToFormat.format(format);
    }

    return dates_cache[data][format];
};
module.exports = formatDate;