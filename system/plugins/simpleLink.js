// Script that parses the [Block] Simple Link from Storyblok

var simpleLink = function(links, classes, attributes) {
    if(!links || !links.length) {
        return '';
    }
    let total_output = '';
    let classes_parts = classes ? classes.split(',') : [];
    let attributes_parts = attributes ? attributes.split(',') : [];

    links.forEach((link, index) => {
        if(!link.text && !module.parent.plugins.storylink(link.link)) {
            return;
        }
        let classes = classes_parts[index] || classes_parts[0] || '';
        let attributes = attributes_parts[index] || attributes_parts[0] || '';
        let url = module.parent.plugins.storylink(link.link);
        let is_video = (url.includes('yout') || url.includes('vimeo'));
        let output = '<a href="{url}" class="{classes}"{attributes}{target}>{text}</a>';
        if(!link.text && link.link.id) {
            var entry = module.parent.plugins.getEntry({id: link.link.id});
            if(entry) {
                link.text = entry.title;
            }
        }

        // No follow, no opener
        if(link.no_follow) {
            if(attributes.includes('rel="') || attributes.includes('rel=\'')) {
                attributes = attributes.replace('rel="', 'rel="nofollow noopener ');
                attributes = attributes.replace('rel=\'', 'rel=\'nofollow noopener ');
            } else {
                attributes += ' rel="nofollow noopener"';
            }
        }
        output = output.replace('{text}', link.text);
        output = output.replace('{target}', link.open_new_tab ? ' target="_blank"':'');
        output = output.replace('{classes}', classes);
        output = output.replace('{url}', is_video ? '#' : url);
        output = output.replace('{attributes}', `${(is_video ? `data-lightbox-video="${url}"` : '')} ${attributes}`);  
        total_output += output;
    });

    return total_output;
}
module.exports = simpleLink;