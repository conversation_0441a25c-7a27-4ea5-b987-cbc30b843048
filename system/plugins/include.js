// Node Modules
const fs = require('fs');
const ejs = require('ejs');
const sitePath = process.cwd();
const utils = require('./../utils');

// Cache
var cache = {};

// Include Plugin
// Usage Example:
// <%= plugins.include('snippets/gmap.html', { width: 300, height: 150, cache: true }) %>
var include = function(filePath, options = {}) {

    // Memorize which page is including what
    if(module.parent.page.path !== undefined) {
        if(module.parent.includes[module.parent.page.path] === undefined) module.parent.includes[module.parent.page.path] = [];
        if(module.parent.includes[module.parent.page.path].indexOf(filePath) < 0) module.parent.includes[module.parent.page.path].push(filePath);
    }
    
    // If there's some cached data, returns it (only if the cache option is set to true)
    var cacheId = filePath + JSON.stringify(options);
    if(options.cache !== undefined && options.cache && cache[cacheId] !== undefined) return cache[cacheId];

    // In case the file doesn't exist
    if(!fs.existsSync(filePath)) {
        return '';
    }

    // Get the file content, the layout etc and compile it
    if(module.parent.templates[filePath] === undefined) {
        module.parent.templates[filePath] = fs.readFileSync(filePath, 'utf-8');
    }
    var toRender = module.parent.templates[filePath];

    try{
        var render = ejs.render(toRender, {
            site: {
                config: module.parent.config,
                pages: module.parent.pages,
                settings: module.parent.plugins.readJSONFile('data/settings.json')
            },
            page: module.parent.page,
            plugins: module.parent.plugins,
            environment: module.parent.environment,
            options: options
        });
    } catch(error) {
        // In case of errors we get an email from the production site
        // or an on-page message on staging
        if(module.parent.environment != 'production') {
            process.stderr.write(`============ ### Error in ${module.parent.page.url} - ${filePath} ============\n\n${error}\n\n============ ### End of Error in ${module.parent.page.url} - ${filePath} ============\n\n\n`);
            return `<div class="build-error">Build Erorr On ${filePath}</div>`;
        } else {
            // utils.sendNotification({
            //     subject: 'Block Rendering Error',
            //     content: `Error rendering block ${filePath} on page ${module.parent.config.url}${module.parent.page.url}`
            // });
            console.log(`Error rendering block ${filePath} on page ${module.parent.config.url}${module.parent.page.url}`, error)
            return ``;
        }
    }

    // Save the cache if the cache option is set to true
    if(options.cache !== undefined && options.cache) cache[cacheId] = render;

    // Return our parsed template
    return render;
    
};
module.exports = include;