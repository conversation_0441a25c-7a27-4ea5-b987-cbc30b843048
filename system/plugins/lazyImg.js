// Cached URLs
var lazyImg = function(image, imgx_options, options) {
    if(!options) {
        options = {};
    }

    // Setting the alt from the image object
    if(typeof image === 'object') {
        options.alt = options.alt || image.alt;
    }
    
    // Preparing the image
    var image_tag = '<img src="{placeholder}" data-src="{url}" {attr} class="lazyload {classes}" alt="{alt}" />';
    var image_url = module.parent.plugins.imgix(image, imgx_options);

    // Placeholder
    var placeholder_options = {blur: 100, q: 5};
    if(imgx_options.w) {
        placeholder_options.w = imgx_options.w / 5;
    }
    if(imgx_options.h) {
        placeholder_options.h = imgx_options.h / 5;
    }
    var placeholder_url = module.parent.plugins.imgix(image, Object.assign({}, imgx_options, placeholder_options));

    // Adding content to the tag
    image_tag = image_tag.replace('{placeholder}', placeholder_url);
    image_tag = image_tag.replace('{url}', image_url);
    if(options.classes) {
        image_tag = image_tag.replace('{classes}', options.classes);
    } else {
        image_tag = image_tag.replace('{classes}', '');
    }
    if(options.alt) {
        image_tag = image_tag.replace('{alt}', options.alt);
    } else {
        image_tag = image_tag.replace('{alt}', 'image');
    }
    if(options.attr) {
        image_tag = image_tag.replace('{attr}', options.alt);
    } else {
        image_tag = image_tag.replace('{attr}', '');
    }
    return image_tag;
}
module.exports = lazyImg;