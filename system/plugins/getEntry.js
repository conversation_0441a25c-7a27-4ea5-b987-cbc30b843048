// Cached URLs
var cachedResults = {slugs:{}, urls: {}, ids:{}};

// Usage Example:
// <%= plugins.getEntry({slug: 'contacts'}) %>
var getEntry = function(options) {
    var pages = module.parent.pages;
    
    if(options.hasOwnProperty('slug')) {
        if(!cachedResults.slugs.hasOwnProperty(options.slug)) {
            for (var i = pages.length - 1; i >= 0; i--) {
                if(pages[i].slug == options.slug) {
                    cachedResults.slugs[options.slug] = Object.assign({}, pages[i]);
                    break;
                }
            }
        }
        return cachedResults.slugs[options.slug];
    }

    if(options.hasOwnProperty('url')) {
        for (var i = pages.length - 1; i >= 0; i--) {
            if(pages[i].url == options.url) {
                cachedResults.urls[options.url] = Object.assign({}, pages[i]);
                break;
            }
        }
        return cachedResults.urls[options.url];
    }

    if(options.hasOwnProperty('id')) {
        for (var i = pages.length - 1; i >= 0; i--) {
            if(pages[i].uuid == options.id) {
                cachedResults.ids[options.id] = Object.assign({}, pages[i]);
                break;
            }
        }
        return cachedResults.ids[options.id];
    }
};
module.exports = getEntry;