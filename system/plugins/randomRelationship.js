const shuffleArray = array => {
    for(let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
};

const randomRelationship = function(field, iterator  = () => {}, noResults  = () => {}) {
    if(field && field.ids && field.ids.length) shuffleArray(field.ids);
    return module.parent.plugins.relationship(field, iterator, noResults);
};

module.exports = randomRelationship;