// Breadcrumbs Plugin
// Usage Example:
// <% plugins.breadcrumbs(entry => { %>
//     <a href="<%- entry.url %>"><%= entry.title %></a>
// <% }) %>
let breadcrumbs = (callback) => {

	var pages = [];
	var page = module.parent.page;

	// Count the number of segments remove one segment at a time
	// to get full parent page url
	var segments = page.url.split('/');
	segments.shift(); // First element always empty
	segments.pop(); // Last element always empty
	for(var i = segments.length; i > 0; i--) {
		var parent_url = segments.slice(0, i).join('/');
		if(parent_url) {
			let entry = module.parent.plugins.getEntry({ url: `/${parent_url}/` });
			if(entry && entry.url !== '/' && entry.url !== '/homepage/') pages.push(entry);
		}
	}
	pages.reverse().forEach(_page => callback(_page));

};
module.exports = breadcrumbs;