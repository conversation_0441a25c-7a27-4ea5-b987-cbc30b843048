// Cached URLs
var imageVideo = function(blocks, html, options) {
    let settings = {
        imgix_q: 60
    };
    if(!blocks || typeof blocks === 'string' || !blocks.length) {
        return '';
    }

    // This is because previously the third parameter was a number with the image width
    if(options && typeof options === 'string' || typeof options === 'number') {
        let width = options;
        options = {};
        options.width = width;
    }

    // Merging settings
    if(options) {
        settings = Object.assign({}, settings, options);
    }

    if(typeof blocks === 'object' && !Array.isArray(blocks)) {
        blocks = [blocks];
    }

    let total_output = '';

    blocks.forEach(block => {
        let url = block.video_url;
        let is_video = url && ((url.includes('yout') || url.includes('vimeo')));
        let output = html;
        output = output.replace('{class}', is_video ? ' video ' : '');
        output = output.replace('{attr}', is_video ? `data-lightbox-video="${url}"` : '');  
        output = output.replace('{alt}', block.alt_text ? block.alt_text : '');
        
        if(settings.lazy_load) {
            output = output.replace('{image}', module.parent.plugins.lazyImg(block.image, { q: settings.imgix_q, w: (settings.width || 800), auto: 'format' }, {alt: block.alt_text}));  
        } else {
            output = output.replace('{image}', module.parent.plugins.imgix(block.image, { q: settings.imgix_q, w: (settings.width || 800), fm: 'jpg' }));  
        }
        
        total_output += output;
    });

    return total_output;
}
module.exports = imageVideo;