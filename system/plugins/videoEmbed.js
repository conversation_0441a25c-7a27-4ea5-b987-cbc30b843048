// Returns the embed code for a video
// Usage Example:
// <%- plugins.videoEmbed(url) %>

var videoEmbed = function(url, options) {
	var video_id = null;
	let code = '';
	let custom_attributes = '';
	if(!options) {
		options = {};
	}

	// Custom attributes
	if(options.custom_attributes) {
		custom_attributes += options.custom_attributes;
	}

	// Retrieve the video id
	var video_regex = /(?:youtube(?:-nocookie)?\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/|vimeo\.com\/)([a-zA-Z0-9_-]{8,11})/;
	var match_id = video_regex.exec(url);
	if(match_id && match_id[1]) {
		video_id = match_id[1];
	}	

	// Returns the embed
	if(options.block_iframe) {
		if(url.includes('youtube') || url.includes('youtu.be')) {
			custom_attributes += ` frameborder='0' webkitallowfullscreen mozallowfullscreen allowfullscreen`
			code = `<iframe data-src='//www.youtube.com/embed/${video_id}?autoplay=1&showinfo=0' ${custom_attributes}></iframe>`;
		} else if(url.includes('vimeo')) {
			custom_attributes += ` frameborder='0' webkitallowfullscreen mozallowfullscreen allowfullscreen`
			code = `<iframe data-src='//player.vimeo.com/video/${video_id}?title=0&amp;byline=0&amp;portrait=0&amp;color=96c159&amp;api=1&amp;autoplay=1&amp;player_id=video_${video_id}' ${custom_attributes}></iframe>`;
		} else code = '';	
	} else {
		if(url.includes('youtube') || url.includes('youtu.be')) {
			code = `<iframe src='//www.youtube.com/embed/${video_id}?autoplay=1&showinfo=0' frameborder='0' webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>`;
		} else if(url.includes('vimeo')) {
			code = `<iframe src='//player.vimeo.com/video/${video_id}?title=0&amp;byline=0&amp;portrait=0&amp;color=96c159&amp;api=1&amp;autoplay=1&amp;player_id=video_${video_id}' frameborder='0' webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>`;
		} else code = '';
	}

	return code;
};
module.exports = videoEmbed;