// Node Modules
const fs = require('fs');

// Cached JSON Files
var JSONFiles = [];

// Include Plugin
// Usage Example:
// <%= plugins.readJSONFile('data/settings.json') %>
var readJSONFile = function(filePath) {
    if(JSONFiles[filePath] === undefined) {
    	var data = fs.readFileSync(filePath, 'utf-8');
        JSONFiles[filePath] = JSON.parse(data);
    }
    return JSONFiles[filePath];
};
module.exports = readJSONFile;