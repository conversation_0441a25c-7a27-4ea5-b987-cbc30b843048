const StoryblokClient = require('storyblok-js-client');

const Storyblok = new StoryblokClient({});

// Defining custom rendering for blocks inside the editor
Storyblok.setComponentResolver((component, block) => {
	return module.parent.plugins.blocks([block]);
});

// Rendering content of the rich text field
// Usage Example:
// <%- plugins.richText(block.description) %>
var richText = function(data, options) {
	// If it's already a string (in case previously this field was a textarea)
	if(typeof data === 'string') {
		return data;
	}

	if (typeof data === 'undefined' || data === null) {
		return '';
	}

	var output = '';
	if(data.content && Array.isArray(data.content)) {
		try {
			output = Storyblok.richTextResolver.render(data);
		} catch(e) {
			output = '';
		}
	}

	if(options && options.strip_html) {
		output = output.replace(/<[^>]*>?/gm, '');
	}

	if(options && options.strip_p) {
		output = output.replace(/<p>/g, '').replace(/<\/p>/g, '');
	}

	return output;
};
module.exports = richText;