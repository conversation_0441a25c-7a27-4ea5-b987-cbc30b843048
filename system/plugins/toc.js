/**
 * This plugin will generate a table of contents based on
 * the blocks that you'll give to this function
 *
 * Usage Example:
 * <%- plugins.toc(page.data.body) %>
 */

module.exports = function(blocks) {
	var TOC = require('table-of-contents-json');
	var toc = new TOC;
	/**
	 * Get specific heading ID
	 * @param  {Object} heading
	 * @return {Number}
	 */
	function getHeadingID(heading) {
		var _id;
		var id = 0;
		blocks.forEach(block => {
			if(block.component !== '[Layer] Heading') return;
			if(heading.id === id) _id = block._uid;
			id++;
		});
		return _id;
	}

	/**
	 * Get UL List for specific Heading (recursive)
	 * @param  {Object} heading
	 * @return {String}
	 */
	function getListForHeading(heading) {
		var output = '<li>';
		output += `<a href="#${getHeadingID(heading)}">${heading.name}</a>`;
		if(heading.children.length) {
			output += '<ul>';
			heading.children.forEach(_heading => { output += getListForHeading(_heading); })
			output += '</ul>';
		}
		output += '</li>';
		return output;
	}

	// First, we'll get the JSON object of the headings blocks
	var html = '';
	blocks.forEach(block => {
		if(block.component !== '[Layer] Heading') return;
		var heading = block;
		html += `<${heading.type} id="${heading._uid}">${heading.text}</${heading.type}>`;
	});
	var headings = toc.generateJSON(html);

	// Then we'll return the generated TOC output
	var output = '<ul>';
	headings.forEach(heading => { output += getListForHeading(heading); });
	output += '</ul>';
	return output;

};