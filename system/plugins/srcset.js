// Cached URLs
var cachedResults = {};

// Usage Example:
// <%= plugins.srcset({url: '/your-image.jpg'}) %>
var srcset = function(url, options) {

	if(!url) {
		return '';
	}

	var parameters = {
		common: {
			fm: 'pjpg',
			lossless: 1
		},
		lg : {
			q: 60,
			w: 1200
		},
		md : {
			q: 60,
			w: 900
		},
		sm : {
			q: 60,
			w: 640
		}
	};

	// Merging options with parameters
	Object.assign(parameters, options);

	// w override for desktop
	if(options.hasOwnProperty('w')) {
		parameters.lg.w = options.w;
	}

	// 1 override for desktop
	if(options.hasOwnProperty('q')) {
		parameters.lg.q = options.q;
	}
		
	// Getting all the properties
	for(var property in options) {
		if(!['lg','md','sm','h','w','q'].includes(property)){
			parameters.common[property] = options[property];
		}
	}

	// Preparing the images
	var desktopImage = module.parent.plugins.imgix(url, Object.assign({}, parameters.common, parameters.lg));
	var tabletImage = module.parent.plugins.imgix(url, Object.assign({}, parameters.common, parameters.md));
	var mobileImage = module.parent.plugins.imgix(url, Object.assign({}, parameters.common, parameters.sm));

	return `${desktopImage} 1024w, ${tabletImage} 768w, ${mobileImage} 320w`;
};
module.exports = srcset;