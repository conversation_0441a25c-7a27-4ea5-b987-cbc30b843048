// Encrypt Plugin
var crypto = require('crypto'),
    algorithm = 'aes-256-cbc',
    key = 'P5CMso4ke2MuFnEBOoLflpgG8LDosIig',
    iv = 'ypHgT4cAdpRPXvf3';

/*
	To decrypt use:

	function decrypt(string){
	  var decipher = crypto.createDecipher(algorithm, key);
	  var dec = decipher.update(string,'hex','utf8');
	  dec += decipher.final('utf8');
	  return dec;
	}
*/

// Usage Example:
// You can either pass a string or a json
// <%- plugins.encrypt('Please encrypt me!') %>
// Will return: fbfa4301b868737aa9areyoureallyevenreadingthis?e742b36ffe03c8f91d5d3403e6af19c108
var encrypt = function(to_encrypt) {
	if(typeof to_encrypt == 'object') {
		to_encrypt = JSON.stringify(to_encrypt);
	}
	let cipher = crypto.createCipheriv(algorithm, Buffer.from(key), iv);
	let encrypted = cipher.update(to_encrypt);
	encrypted = Buffer.concat([encrypted, cipher.final()]);
	return encrypted.toString('hex');
};
module.exports = encrypt;


