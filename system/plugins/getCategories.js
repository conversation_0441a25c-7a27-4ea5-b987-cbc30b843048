// Cached JSON Files
var cachedCategories = {};

// Get Categories Plugin
// Usage Example:
// <%= plugins.getCategories('News Module') %>
var getCategories = function(component) {
    if(cachedCategories[component] === undefined) {
    	var pages = module.parent.pages;
    	var categories = [];
    	for(var i = 0; i < pages.length; i++) {
    	    var page = pages[i];
    	    if(page.data !== undefined && page.data.category !== undefined && page.data.component == component && categories.indexOf(page.data.category) < 0) {
    	        categories.push(page.data.category);
    	    }
    	}
        cachedCategories[component] = categories;
    }
    return cachedCategories[component];
};
module.exports = getCategories;