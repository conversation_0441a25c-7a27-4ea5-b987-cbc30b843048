// Debug Data
// Usage Example:
// <%- plugins.debugData(page.data) %>
var debugData = function(data) {
	var id = Math.random().toString(36).substr(2, 9);
	return `
		<div class="container"><div id="debug-block-${id}" class="json-viewer"></div></div>
		<script>
			if(typeof jsonViews === 'undefined') {
				function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){"function"==typeof define&&define.amd?define([],function(){return t(e)}):"object"===("undefined"==typeof exports?"undefined":_typeof(exports))?module.exports=t(e):e.jsonViewer=t(e)}("undefined"!=typeof global?global:"undefined"!=typeof window?window:void 0,function(l){function c(e){return e instanceof Object&&0<Object.keys(e).length}return function(e,t,n){var o=function e(t,n){var o="";if("string"==typeof t)t=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),n.withLinks&&function(e){return/^(ftp|http|https):\\/\\/(\\w+:{0,1}\\w*@)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%@!\\-\\/]))?/.test(e)}(t)?o+='<a href="'+t+'" class="json-string" target="_blank">'+t+"</a>":o+='<span class="json-string">"'+t+'"</span>';else if("number"==typeof t)o+='<span class="json-literal">'+t+"</span>";else if("boolean"==typeof t)o+='<span class="json-literal">'+t+"</span>";else if(null===t)o+='<span class="json-literal">null</span>';else if(t instanceof Array)if(0<t.length){o+='[<ol class="json-array">';for(var l=0;l<t.length;++l)o+="<li>",c(t[l])&&(o+='<span class="json-toggle"></span>'),o+=e(t[l],n),l<t.length-1&&(o+=","),o+="</li>";o+="</ol>]"}else o+="[]";else if("object"==typeof t){var s=Object.keys(t).length;if(0<s){for(var a in o+='{<ul class="json-dict unstyled">',t)if(t.hasOwnProperty(a)){o+="<li>";var r=n.withQuotes?'<span class="json-string">"'+a+'"</span>':a;c(t[a])?o+='<span class="json-toggle">'+r+"</span>":o+=r,o+=": "+e(t[a],n),0<--s&&(o+=","),o+="</li>"}o+="</ul>}"}else o+="{}"}return o}(e,n=Object.assign({},{collapsed:!1,rootCollapsable:!0,withQuotes:!1,withLinks:!0},n));n.rootCollapsable&&c(e)&&(o='<span class="json-toggle"></span>'+o),t.innerHTML=o,t.classList.add("json-document"),t.querySelectorAll(".json-toggle").forEach(function(e){e.addEventListener("click",function(e){e.preventDefault(),e.target.classList.contains("collapsed")?e.target.classList.remove("collapsed"):e.target.classList.add("collapsed"),e.target.parentNode.querySelectorAll("ul.json-dict, ol.json-array").forEach(function(e){if("none"===l.getComputedStyle(e).display)e.style.display="block",e.parentNode.querySelectorAll(".json-placeholder").forEach(function(e){e.remove()});else{e.style.display="none";var t=e.querySelectorAll("li").length,n=t+(1<t?" items":" item");e.insertAdjacentHTML("afterend",'<span class="json-placeholder">'+n+"</span>")}})})}),t.querySelectorAll(".json-placeholder").forEach(function(e){e.addEventListener("click",function(e){e.preventEventDefault(),e.target.parentNode.querySelectorAll(".json-toggle").forEach(function(e){e.click()})})}),1==n.collapsed&&t.querySelectorAll(".json-toggle").forEach(function(e){e.click()})}});
			}
			var block = ${JSON.stringify(data)};
			jsonViewer(block, document.body.querySelector('#debug-block-${id}'), { collapsed: false })
		</script>
		<style type="text/css">
			div.json-viewer{background-color:#2d3b45;color:#fff;font-family:monospace,monospace;padding:40px 50px;font-size:12px;box-shadow:0 14px 34px rgba(0,0,0,.4);position:relative;margin:40px 0}div.json-viewer:before{content:"BLOCK DATA";position:absolute;right:10px;top:3px;font-size:17px;letter-spacing:-2px;color:#00d0ff;font-weight:700;font-family:Helvetica,Arial,sans-serif;background:-webkit-linear-gradient(left,#00d0ff,#c000ff);-webkit-background-clip:text;padding-right:2px;-webkit-text-fill-color:transparent}span.bracket{font-weight:700;font-size:11px;color:#fff;-webkit-font-smoothing:subpixel-antialiased;-moz-osx-font-smoothing:grayscale}ol.json-array,ul.json-dict{list-style-type:none;margin:0 0 0 1px;border-left:1px solid rgba(204,204,204,.07);padding-left:20px;margin-left:3px!important}ol.json-array li,ul.json-dict li{line-height:1.5!important;margin-bottom:0!important;font-size:12px!important;-webkit-font-smoothing:subpixel-antialiased!important;-moz-osx-font-smoothing:grayscale!important;color:#00d3fb!important}ol.json-array li:before,ul.json-dict li:before{display:none!important}.json-string{color:#ea4d80}.json-literal{color:#1a01cc;font-weight:700}span.json-toggle{cursor:pointer;position:relative;color:inherit;text-decoration:none;font-weight:600}span.json-toggle:focus{outline:0}span.json-toggle:before{content:"";position:absolute;left:-14px;top:5px;width:0;height:0;border-style:solid;border-width:6.9px 4px 0 4px;border-color:#fff transparent transparent transparent}span.json-toggle.collapsed:before{transform:rotate(29deg);-ms-transform:rotate(29deg);-webkit-transform:rotate(29deg)}span.json-placeholder{color:#aaa;padding:0 1em;text-decoration:none}span.json-placeholder:hover{text-decoration:underline}
		</style>
	`;
};
module.exports = debugData;