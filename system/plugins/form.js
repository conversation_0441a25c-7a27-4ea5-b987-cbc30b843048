// Cached URLs
var form = function(options) {

    // Getting the content of the form
    if(options.template && options.template !== 'none') {
        var form_content = module.parent.plugins.include(`snippets/forms/${options.template}.html`);
    } else {
        options.exclude_fields_from_mail = options.exclude_fields_from_mail || '';
        // Getting fields to hide in the email 
        if(options.fields && options.fields.length) {
            let exclude_from_mail = [];
            options.fields.forEach( field => {
                if(field.exclude_from_mail) {
                    exclude_from_mail.push(field.name);
                }
            });
            options.exclude_fields_from_mail += exclude_from_mail.join(',');
        }
        var form_content = module.parent.plugins.blocks(options.fields);
    }

    return module.parent.plugins.include(`snippets/forms/layout.html`,  Object.assign({'form_content': form_content}, options));
}
module.exports = form;