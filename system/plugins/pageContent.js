// Already checked existing files
var exists = {};

// Page Content Plugin
// Usage Example:
// <%= plugins.pageContent(page.data) %>
// Will return: blocks content
var pageContent = function(data) {
    var render = '';
    var block = data;
    var blockName = block.component;
    var blockSlug = module.parent.plugins.slugify(blockName).replace(/-/g, '_');
    var is_module = blockSlug.endsWith("_module");

    if ( is_module ) {
        // If it's a module I need to change the name of the component to match the block it requires
        // i.e. News Module => [Block] News Detail
        render += module.parent.plugins.blocks([Object.assign({}, block, {component: `[Block] ${blockName.replace(' Module', ' Detail')}`})]);
    } else {
        // If it's a standard pages it just outputs the blocks
        render += module.parent.plugins.blocks((block.content) ? block.content : block.body);
    }

    return render;
};
module.exports = pageContent;