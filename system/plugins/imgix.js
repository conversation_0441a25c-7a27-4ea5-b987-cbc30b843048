// Node Modules
const ImgixClient = require('imgix-core-js');

// Create the Imgix Client Instance only one time
var client = new ImgixClient({
    host: module.parent.config.imgix.source,
    secureURLToken: module.parent.config.imgix.secure_url_token
});

var storyblokImageService = function(src, quality, width, height) {
    const parameters = []
    const filters = []

    if (width || height) parameters.push(`${parseInt(width || 0)}x${parseInt(height || 0)}`)
    filters.push(`quality(${quality || 60})`)
    parameters.push(`filters:${filters.join(':')}`)
    const storyblokUrl = src += `/m/${parameters.join('/')}`
    return storyblokUrl.replace(/\(/g, '\\(').replace(/\)/g, '\\)')
}

// IMGIX Plugin
// Usage Example:
// <%= plugins.imgix(image_url, { w: 300, h: 150 }) %>
var imgix = function(url, options) {

    if(!url) {
        return '';
    }

    // Support for the new assets field in Storyblok
    if(typeof url === 'object') {
        if(!url.filename) {
            return '';
        }
        url = url.filename;
    }

    // Use the storyblok image service
    if (url.indexOf('a.storyblok.com/f/') >= 0 && !module.parent.config.force_imgix) {
        if (typeof options === 'object') {
            url = storyblokImageService(url, options.q || 60, options.w, options.h);
        }
        return url;
    }

    url = url.replace('https://s3.amazonaws.com/', 'https://')

    // Cleaning the url in case it has https or http prepended
    url = url.replace(/^(http:|https:)/g, '');

    if(module.parent.config.assets_url_masking) {
        url = url.replace(`//${module.parent.config.assets_url_masking}/f/`, '');
        var imgixUrl = client.buildURL(url, options);
        imgixUrl = imgixUrl.replace(module.parent.config.imgix.source, module.parent.config.assets_url_masking);
    } else {
        url = url.replace(module.parent.config.imgix.to_remove_from_url, '');
        var imgixUrl = client.buildURL(url, options);
    }
    
    return imgixUrl;
};
module.exports = imgix;