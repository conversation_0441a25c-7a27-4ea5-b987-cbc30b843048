// Cached URLs
var cachedUrls = {};

// Url By Uid Plugin
// Usage Example:
// <%= plugins.urlByUid(uid) %>
var urlByUid = function(uid) {
    var pages = module.parent.pages;
    var length = pages.length;
    var pageUrl = '';
    if(cachedUrls[uid] === undefined) {
        for(var i = 0; i < length; i++) {
            var page = pages[i];
            if(page.uuid !== undefined && page.uuid == uid) {
                cachedUrls[uid] = page.url;
                pageUrl = page.url;
                break;
            }
        }
    } else pageUrl = cachedUrls[uid];
    return pageUrl;
};
module.exports = urlByUid;