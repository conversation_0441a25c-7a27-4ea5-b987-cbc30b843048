// Cached URLs
var cachedResults = {};

// Usage Example:
// <%= plugins.getEntries({slug: 'contacts'}) %>
// <%= plugins.getEntries({folder: 'about'}) %>
// <%= plugins.getEntries( entry => entry.url.includes('/about'), true) %>

var getEntries = function(options, multitple_values = false) {
    var pages = module.parent.pages.concat([]);
    
    // Override multiple values options
    if(options.folder) {
        multitple_values = true;
    }

    // Get the folder from the sitemap
    function findFolder(pages_group, url) {
        var pages_out = [];
        var folder = pages_group.find( page => page.module == 'Folder' && page.url === `/${url.replace(/\/$/, '').replace(/^\//, '')}/`);

        if(pages_group.length) {
            pages_group.forEach(page => {
                if(page.hasOwnProperty('sub_stories')) {
                    if(!folder) {
                        folder = findFolder(page.sub_stories, url);
                    }
                }
            });
        }

        return folder;
    }

    // Check if the type of options is a parameter
    // or a function
    if(typeof options === 'object') {
        var cache_name = JSON.stringify(options); // Getting a string out of the parameters

        // In case the result already exists
        if(cachedResults[cache_name]) {
            pages = cachedResults[cache_name];
        } else {
            // Filter pages by the given parameters
            if(Object.keys(options).length) {
                for(var key in options) {
                    switch(key) {
                        // Get siblings of a url
                        case 'folder':
                            var page_url = options[key];
                            var sitemap = module.parent.plugins.readJSONFile('data/sitemap.json');
                            var folder = findFolder(sitemap, page_url);

                            // If there are no siblings
                            if(!folder) {
                                pages = [];
                                break;
                            }
                            
                            var sub_pages = [];

                            // Getting pages objects
                            folder.sub_stories.forEach( page => {
                                // If it's a folder return the starting page if set and 
                                // create a folder property inside the story data with the
                                // data of the folder
                                if(page.module === 'Folder') {
                                    if(page.starting_page) {
                                        var sibling = pages.find( sub_page => sub_page.id == page.starting_page.id);
                                        var folder_copy = Object.assign({}, page);
                                        delete folder_copy.sub_stories;
                                        sibling.folder = folder_copy;
                                        sub_pages.push(sibling);
                                    }
                                } else {
                                    var sibling = pages.find( sub_page => sub_page.id == page.id);
                                    // If it's the starting page of the folder
                                    // create a folder property inside the story data with the
                                    // data of the folder
                                    if(sibling.is_startpage) {
                                        var folder_copy = Object.assign({}, folder);
                                        delete folder_copy.sub_stories;
                                        sibling.folder = folder_copy;
                                    }
                                    sub_pages.push(sibling);
                                }
                            });
                            //var sub_pages = pages.filter( page => siblings.map( sub_page => sub_page.id ).includes(page.id) ).sort( (a,b) => a.position > b.position );

                            // Return folder + sub stories
                            pages = sub_pages;
                            break;
                        // Checking a property of an entry
                        default: 
                            pages = pages.filter( page => eval(`page.${key}`) == options[key] );
                            break;
                    }
                }
                if(!multitple_values) {
                    pages = pages[0];
                }
            }
        }

    } else if(typeof options === 'function') {
        // If options is a function, use it to filter the entries
        var cache_name = options.toString().replace(/(\r\n\t|\n|\r\t|\s)/gm,"");

        // In case the result already exists
        if(cachedResults[cache_name]) {
            pages = cachedResults[cache_name];
        } else {
        // Filter pages by given function
            if(multitple_values) {
                pages = pages.filter( page => options(page) );
            } else {
                pages = pages.find( page => options(page) );
            }   
        } 
    }
    
    return pages;
};
module.exports = getEntries;