// Node Modules
const utils = require('./../utils');
const ejs = require('ejs');
const fse = require('fs-extra');

// Memorize each page for pagination
var currentPage = {};

// Memorize which stories requests are over
var overRequests = {};

// Memorize which category we should generate
var currentCategory = {};

// Memorize which categories we generated too
var generatedCategories = {};

// Stores pagination
var paginationCache = {}

// Stories Plugin
// Usage Example:
// <!-- News Listing -->
// <% plugins.stories({
//     component: 'News Module',
//     limit: 2,
//     sort: 'desc',
//     order_by: 'created_at', // Or data.whatever_the_field_you_want, see this like an object
//     paginate: true,
//     context: page.slug, // For preventing cached results
//     categories: ['industry-news'],
//     categoriesPages: false, // true if you want to automatically generate categories pages
//     where: (news) => news.slug == '10-reasons-why-batman-is-great',
//     manipulate: (news) => news.shift(),
//     just_list: true // true if you don't want next and prev objects appended to the story object,
//     before: (news, categories) => {JSON.stringiry(news); JSON.stringiry(categories);}, // Executed before the loop
//     after: (news, categories) => {JSON.stringiry(news); JSON.stringiry(categories);} // Executed after the loop
// }, (news) => { %>
//     <!-- We got some results, list the news! -->
//     <div class="columns small-12 news-item">
//         <h4><%= news.title %></h4>
//         <p><%= news.data.excerpt %></p>
//         <a href="<%= news.url %>" class="button">Read More</a>
//         <a href="<%= news.previous.url %>" class="button">Previous News (<%= news.previous.title %>)</a>
//         <a href="<%= news.next.url %>" class="button">Next News (<%= news.next.title %>)</a>
//     </div>
// <% }, () => { %>
//     <!-- Uh oh, we didn't find any news :( -->
//     <div class="columns small-12">
//         <p>Sorry, there's currently no news in the database.</p>
//     </div>
// <% }) %>
var stories = function(parameters, success = () => {}, error = () => {}) {

    // Categories override (automatic categories pages generation)
    if(currentCategory[module.parent.page.id] !== undefined && parameters.categoriesPages) parameters.categories = [currentCategory[module.parent.page.id]];
    
    // Parameters
    parameters = prepareParameters(parameters);

    // Context ID, we'll use it for cache purposes
    var contextID = (module.parent.page.url.split('/')[1]) + module.parent.page.id + parameters.categories.join();

    // Get the stories (with cache system)
    var stories = getStories(parameters, contextID);

    // No stories found :(
    if(!stories.length) {
        return error();
    }

    // Get the total number of stories
    var totalStories = stories.length;

    // We'll use pagination for this one, let's define the current page
    // and count the total number of pages
    if(parameters.paginate) {
        if(currentPage[contextID] === undefined) currentPage[contextID] = 1;
        var page = currentPage[contextID];
        var totalPages = Math.ceil(totalStories / parameters.limit);
    }

    // Limit
    var start = (parameters.paginate) ? parameters.limit * (page - 1) : 0;
    var end = (parameters.paginate) ? start + parameters.limit : parameters.limit;
    var slicedStories = stories.slice(start, end);   


    // List the stories
    var length = slicedStories.length;

    for(var i = 0; i < length; i++) {
        var story = slicedStories[i];

        if(!parameters.just_list) {
            // Get previous & next stories
            var index = start + i;
            story.previous = (stories[index - 1] !== undefined) ? stories[index - 1] : stories[stories.length - 1];
            story.next = (stories[index + 1] !== undefined) ? stories[index + 1] : stories[0];
        }

        // Adding total elements number and current index
        story.total = length;
        story.count = i;

        // List the results
        success(story);
    }

    // Create pagination pages (with categories)
    var categoryUrl = (currentCategory[module.parent.page.id] !== undefined && parameters.categoriesPages) ? '/category/' + module.parent.plugins.slugify(currentCategory[module.parent.page.id]) : '';

    // Generate pagination links
    var pages = [];
    if(parameters.paginate) {
        var previousPage = {};
        var nextPage = {};
        var length = stories.length;
        if(module.parent.page.url.indexOf('/category/') > -1) {
            var base_url =  module.parent.page.url.substring(0, module.parent.page.url.indexOf('/category/'));
        } else if(module.parent.page.url.indexOf('/page-') > -1) {
            var base_url =  module.parent.page.url.substring(0, module.parent.page.url.indexOf('/page-'));
        } else {
            var base_url = module.parent.page.url;
        }
        base_url = base_url.replace(/\/$/, '').replace(/^\//, ''); // removing slashes at the beginnig and at the end
        for(var i = 1; i <= totalPages; i++) {
            // Page Slug URL
            var pageSlug = (i == 1) ? '' : 'page-' + i;
            // Find the page url
            var pageUrl = ( `/${base_url}${categoryUrl}/${pageSlug}/`).replace(/\/\//g, '/');

            // Previous & Next Pages
            if(i == page - 1) {
                previousPage = {
                    number: i,
                    url: pageUrl
                };
            }
            if(i == page + 1) {
                nextPage = {
                    number: i,
                    url: pageUrl
                };
            }

            pages.push({
                number: i,
                current: (i == page),
                url: pageUrl
            });

        }
    }
 
    if(parameters.paginate && page == 1) {
        // Create the page
        var newPage = Object.assign({}, module.parent.page);
        var current_page = module.parent.page; // Save the current page to restore it at the end of the categories generation

        for(var i = 1; i < totalPages; i++) {
            // Increment the page Number
            currentPage[contextID]++;
    
            var filePath = (module.parent.config.build.folder + `/${(base_url)}` + categoryUrl + '/page-' + currentPage[contextID] + '/index.html').replace(/\/\//g, '/');
            
            newPage.url = `/${base_url}${categoryUrl}/page-${currentPage[contextID]}`;
            newPage.page_number = currentPage[contextID];

            var newPageGeneration = new Promise(function(resolve, reject){
                utils.renderPage(filePath, newPage);
                resolve();
            });
        }
        
        module.parent.page = current_page;// restoring initial page
    }

    // This if statement is executed whenever a stories request is over
    if(((parameters.paginate && page >= totalPages) || !parameters.paginate) && overRequests[contextID] === undefined && parameters.categoriesPages) {
        overRequests[contextID] = true;

        // Get all the categories for the selected stories (with cache)
        var categories = getStoriesCategories(parameters, stories);

        // Now go each categories and generate the pages
        var current_page = module.parent.page; // Save the current page to restore it at the end of the categories generation
        if(categories.length) {
            if(generatedCategories[module.parent.page.id] === undefined) generatedCategories[module.parent.page.id] = [];
            var length = categories.length;
            for(var i = 0; i < length; i++) {
                var category = categories[i];
                if(generatedCategories[module.parent.page.id].indexOf(category) < 0) {
                    generatedCategories[module.parent.page.id].push(category);
                    currentCategory[module.parent.page.id] = category;
                    var filePath = (module.parent.config.build.folder + '/' + base_url + '/category/' + module.parent.plugins.slugify(category) + '/index.html').replace(/\/\//g, '/');
                    var newPage = Object.assign({}, module.parent.page, { url: '/' + base_url + '/category/' + module.parent.plugins.slugify(category) });
                    var newCategoryPageGeneration = new Promise(function(resolve, reject){
                        utils.renderPage(filePath, newPage);
                        resolve();
                    });
                }
            }
        }
        module.parent.page = current_page;// restoring initial page
    }

    // Return all the sliced stories with pagination
    return {
        stories: slicedStories,
        pagination: {
            pages: pages,
            currentPage: page,
            previous: previousPage,
            next: nextPage
        }
    };

};

// ============================================================================ //
// Helpful Methods
// =============== //

// Prepare Parameters
var prepareParameters = function(parameters) {
    return {
        component: (parameters.component !== undefined) ? parameters.component : '',
        manipulate: (parameters.manipulate !== undefined) ? parameters.manipulate : undefined,
        sort: (parameters.sort !== undefined) ? parameters.sort : 'desc',
        order_by: (parameters.order_by !== undefined) ? parameters.order_by : 'position',
        paginate: (parameters.paginate !== undefined) ? parameters.paginate : false,
        limit: (parameters.limit !== undefined) ? parameters.limit : 100,
        just_list: (parameters.just_list !== undefined) ? parameters.just_list : false,
        categories: (parameters.categories !== undefined) ? parameters.categories : [],
        categoriesPages: (parameters.categoriesPages !== undefined) ? parameters.categoriesPages : false,
        where: (parameters.where !== undefined) ? parameters.where : () => (true),
        before:  (parameters.before !== undefined) ? parameters.before : () => '',
        after:  (parameters.after !== undefined) ? parameters.after : () => '',
        context: (parameters.context !== undefined) ? parameters.context : '',
    };
}

// Get all stories based on parameters
var storiesCache = {};
var getStories = function(parameters, contextID) {
    var stories = [];
    var cacheId = JSON.stringify(parameters);
    if(storiesCache[cacheId] === undefined) {
        var pages = module.parent.pages;
        var length = pages.length;
        for(var i = 0; i < length; i++) {
            var page = pages[i];
            if(page.data !== undefined) {

                // Should we add this page?
                let toAdd = true;

                // Look for correct components first
                if(parameters.component && (page.data.hasOwnProperty('component') &&  page.data.component != parameters.component) ||  (!page.data.hasOwnProperty('component') && page.data.component != parameters.component) )  toAdd = false;

                // Categories
                if(parameters.categories && parameters.categories.length && page.data.category !== undefined) {

                    // Multiple categories (array)
                    if(page.data.category.constructor === Array) {
                        if(!parameters.categories.some(r => page.data.category.includes(r))) toAdd = false;

                    // String category (single)
                    } else if(parameters.categories.indexOf(page.data.category) < 0) toAdd = false;

                }

                // Where
                if(!parameters.where(page)) toAdd = false;

                // Add the page to the list, if criterias are correct
                if(toAdd) stories.push(page);

            }
        }
        // Order By & Sort
        if(parameters.order_by) {
            stories.sort(function(a, b) {
                // Catching errors in case an entry doesn't have the order by property
                try {
                    a = eval(`a.${parameters.order_by}`);
                    b = eval(`b.${parameters.order_by}`);
                    if(a < b) return -1;
                    else if(a > b) return 1;
                    else return 0;
                } catch(e) {
                    return 0;
                }
            });
        }
        if(parameters.sort == 'desc') stories.reverse();

        // Manipulate the stories
        var manipulate = (parameters.manipulate !== undefined) ? parameters.manipulate : (stories) => stories;
        stories = manipulate(stories);

        storiesCache[cacheId] = JSON.parse(JSON.stringify(stories));
    }

    return storiesCache[cacheId]; // Clone the result, blame JS for that :(
};

// Get specific stories categories
var categoriesCache = {};
var getStoriesCategories = function(parameters, stories) {
    var categories = [];
    var cacheId = JSON.stringify(parameters);
    if(categoriesCache[cacheId + module.parent.page.id] === undefined) {
        var length = stories.length;
        for(var i = 0; i < length; i++) {
            var story = stories[i];
            var category = stories[i].data !== undefined && stories[i].data.category !== undefined ? stories[i].data.category : false;
            category = (category.constructor === Array) ? category : [category];
            for(var n = 0; n < category.length; n++) {
                var cat = category[n];
                if(cat && !categories.includes(cat)) {
                    categories.push(cat);
                }
            }
        }
        categoriesCache[cacheId + module.parent.page.id] = categories;
    } else {
        categories = categoriesCache[cacheId + module.parent.page.id];
    }
    return categories;
};

module.exports = stories;