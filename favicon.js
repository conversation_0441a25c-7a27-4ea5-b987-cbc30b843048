// Node Modules
const Jimp = require('jimp');
const sitePath = process.cwd();
const original_favicon = `${sitePath}/assets/images/favicons/favicon.png`;
const favicon_sizes = [32, 128, 152, 167, 180, 192, 196];

Jimp.read(original_favicon, (err, image) => {
  if (err) throw err;
  favicon_sizes.forEach(size => {
  	image
    	.resize(size, size) // resize
    	.write(`${sitePath}/assets/images/favicons/favicon-${size}.png`);
  });
   // save
});