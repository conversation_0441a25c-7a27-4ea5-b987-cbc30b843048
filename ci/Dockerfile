FROM node:16.19.1-bullseye
LABEL name="flash-2024-builder"

# Install additional packages and Amazon Corretto 8
RUN apt-get update && apt-get install -y \
    python3 \
    git \
    unzip \
    curl \
    ruby \
    wget \
    gnupg \
    lftp \
    software-properties-common \
    chromium \
    && wget -O- https://apt.corretto.aws/corretto.key | gpg --dearmor -o /usr/share/keyrings/corretto-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/corretto-keyring.gpg] https://apt.corretto.aws stable main" | tee /etc/apt/sources.list.d/corretto.list \
    && apt-get update \
    && apt-get install -y java-1.8.0-amazon-corretto-jdk \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set JAVA_HOME
ENV JAVA_HOME=/usr/lib/jvm/java-1.8.0-amazon-corretto-jdk

# Set environment variable to skip <PERSON>uppet<PERSON>'s Chromium download
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV CHROMIUM_PATH=/usr/bin/chromium

WORKDIR /root

# Setup global npm configurations
RUN npm config set loglevel warn && \
    npm config set audit false && \
    npm config set fund false && \
    npm config set maxsockets 50 && \
    npm config set prefer-offline true && \
    npm config set omit dev && \
    npm config set send-metrics false && \
    npm config set engine-strict true && \
    npm config set update-notifier false

# Install npm@9.9.3 globally
RUN npm install -g npm@9.9.3

# Copy the application code
COPY . /root/flash-2024/

WORKDIR /root/flash-2024

# Install flash-2024 globally
RUN npm ci && \
    npm link

# Set GEM_HOME and add to PATH
ENV GEM_HOME=/root/.gem
ENV PATH=/root/.gem/bin:$PATH

# Install gems
RUN gem install s3_website bundler

# Echo versions and configurations
RUN echo "Node version: $(node -v)" && \
    echo "npm version: $(npm -v)" && \
    echo "npm config list:" && npm config list && \
    echo "Python version: $(python3 --version)" && \
    echo "Ruby version: $(ruby --version)" && \
    echo "Gem version: $(gem --version)" && \
    echo "s3_website version: $(s3_website --version)" && \
    echo "Bundler version: $(bundler --version)" && \
    echo "Java version: $(java -version)" && \
    echo "Chromium version: $(chromium --version)"

WORKDIR /root
CMD ["/bin/bash"]