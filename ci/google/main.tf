# =====================================================================
# Input Variables
# =====================================================================
variable "project_name" {
  description = "A name for the whole project"
  type        = string
  default     = "flash-2024-builder"
  validation {
    condition     = length(var.project_name) > 0
    error_message = "The project name must not be empty"
  }
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "The project name must only contain lowercase letters, numbers, and hyphens"
  }
}

variable "region" {
  description = "The region to deploy resources in"
  type        = string
  default     = "europe-west2"
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.region))
    error_message = "The region must only contain lowercase letters, numbers, and hyphens"
  }
}

variable "org_id" {
  description = "The organization ID"
  type        = string
  default     = "*************"
  validation {
    condition     = length(var.org_id) > 0
    error_message = "The organization ID must not be empty"
  }
  validation {
    condition     = can(regex("^[0-9]{4,}$", var.org_id))
    error_message = "The organization ID must be a number with at least four digits"
  }
}

variable "billing_account" {
  description = "The billing account ID"
  type        = string
  default     = "018446-E1C3B9-E4E116"

}

# =====================================================================
# Provider Configuration
# =====================================================================
provider "google-beta" {
  region = var.region
}

# =====================================================================
# Project Settings
# =====================================================================
resource "random_integer" "project_id" {
  min = 1000
  max = 9999
}

locals {
  project_id = "${var.project_name}-${random_integer.project_id.result}"
}

resource "google_project" "project" {
  name                = var.project_name
  project_id          = local.project_id
  org_id              = var.org_id
  billing_account     = var.billing_account
  auto_create_network = false
}

# =====================================================================
# API Services
# =====================================================================
module "project_services" {
  source                      = "terraform-google-modules/project-factory/google//modules/project_services"
  disable_services_on_destroy = true
  project_id                  = google_project.project.project_id
  enable_apis                 = true

  activate_apis = [
    "iam.googleapis.com",
    "artifactregistry.googleapis.com"
  ]
}

# =====================================================================
# Artifact Registry Repository
# =====================================================================
resource "google_artifact_registry_repository" "repository" {
  provider      = google-beta
  location      = var.region
  project       = google_project.project.project_id
  repository_id = var.project_name
  format        = "DOCKER"

  docker_config {
    immutable_tags = false

  }


  cleanup_policies {
    id     = "keep-latest-only"
    action = "KEEP"
    most_recent_versions {
      keep_count = 1
    }
  }


  depends_on = [module.project_services]
}

# Add IAM policy binding to make the repository public
resource "google_artifact_registry_repository_iam_member" "public_reader" {
  provider   = google-beta
  project    = google_project.project.project_id
  location   = google_artifact_registry_repository.repository.location
  repository = google_artifact_registry_repository.repository.name
  role       = "roles/artifactregistry.reader"
  member     = "allUsers"
}
# =====================================================================
# Service Account for GitHub Actions
# =====================================================================
resource "google_service_account" "github_actions" {
  project      = google_project.project.project_id
  account_id   = "${var.project_name}-gh-actions"
  display_name = "Service Account for GitHub Actions"
  depends_on   = [google_project.project]
}

resource "google_service_account_key" "github_actions_key" {
  service_account_id = google_service_account.github_actions.name
}

# =====================================================================
# IAM Configuration
# =====================================================================
resource "google_artifact_registry_repository_iam_member" "github_actions_writer" {
  provider   = google-beta
  project    = google_project.project.project_id
  location   = google_artifact_registry_repository.repository.location
  repository = google_artifact_registry_repository.repository.name
  role       = "roles/artifactregistry.writer"
  member     = "serviceAccount:${google_service_account.github_actions.email}"
}

# =====================================================================
# Outputs
# =====================================================================

# Add to your locals section
locals {
  container_repository_url = "${var.region}-docker.pkg.dev/${google_project.project.project_id}/${var.project_name}"
}

# Update your outputs
output "repository_url" {
  value       = local.container_repository_url
  description = "The URL of the Artifact Registry repository"
}

output "credentials_json" {
  value       = google_service_account_key.github_actions_key.private_key
  description = "Service account JSON key"
  sensitive   = true
}

output "github_actions_setup" {
  value = <<EOT
# GitHub Actions Variables Setup

Add these repository variables:
GCP_REPOSITORY_URL: ${local.container_repository_url}

Add this repository secret:
GCP_CREDENTIALS: <The credentials_json output value (service account key)>

EOT
}
