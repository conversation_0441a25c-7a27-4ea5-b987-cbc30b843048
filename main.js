#!/usr/bin/env node
const fs = require("fs-extra");
const path = require("path");
const source = path.dirname(fs.realpathSync(__filename));
const { exec } = require("child_process");

// Runs a new process and handles errors
var run = (commandLine) => {
	var commandProcess = exec(commandLine);
	commandProcess.stdout.on("data", (data) => process.stdout.write(data));
	commandProcess.stderr.on("data", (data) => process.stderr.write(data));

	commandProcess.on("exit", (code) => {
		if (code !== 0) {
			console.error(`\nCommand failed with exit code ${code}`);
			process.exit(code);
		}
	});
};

// Get command name & path
var command = process.argv[2] || "";
var parameters = process.argv.slice(3).join(" ");
var commandPath = `${source}/commands/${command}.js`;

// Run it
if (fs.existsSync(commandPath)) {
	run(`node --max-old-space-size=8192 ${commandPath} ${parameters}`);
} else {
	console.error("Command not found");
	process.exit(1);
}
