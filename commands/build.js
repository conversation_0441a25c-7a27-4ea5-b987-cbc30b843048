// Node Modules
const HeadlessFlash = require("./../system/headless-flash");
const scriptHooks = require("./../system/script-hooks");
var environment = "dev";
const fs = require("fs");
const sitePath = process.cwd();
const config = require(`${sitePath}/config`);

// Any specific build path?
var file, entry_id;
process.argv.forEach((argument) => {
	if (argument.indexOf("--file=") >= 0) file = argument.replace("--file=", "");
	if (argument.indexOf("--entry-id=") >= 0)
		entry_id = argument.replace("--entry-id=", "");
	if (argument.indexOf("--prod") >= 0) environment = "production";
});

// Execute prebuild hook and then build
(async () => {
	try {
		await scriptHooks.prebuild();
	} catch (error) {
		console.error("Warning: prebuild hook failed:", error.message);
	}

	// First the script will check if the storyblok folder is empty
	fs.readdir(
		`${sitePath}/pages/${config.storyblok.folder}/`,
		async (error, files) => {
			if (error || !files || !files.length) {
				throw new Error("Storyblok folder is empty. Build process aborted");
			} else {
				// Build the website or just one page?
				console.time("Build Execution Time");
				// Buliding the whole site
				if (!file && !entry_id) HeadlessFlash.build(environment);
				// Building new entry
				else if (file) HeadlessFlash.buildPage(file, environment);
				// Building entries with updated content
				else if (entry_id) HeadlessFlash.buildEntry(entry_id, environment);
				console.timeEnd("Build Execution Time");

				// Execute postbuild hook
				try {
					await scriptHooks.postbuild();
				} catch (error) {
					console.error("Warning: postbuild hook failed:", error.message);
				}
			}
		}
	);
})();
