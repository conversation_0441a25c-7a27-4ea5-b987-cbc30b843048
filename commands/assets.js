// Required node modules
const fs = require("fs").promises;
const fsSync = require("fs");
const { glob } = require("glob");
const sass = require("node-sass");
const cleanCss = require("clean-css");
const uglifyJs = require("uglify-js");
const path = require("path");
const utils = require("./../system/utils");
const scriptHooks = require("./../system/script-hooks");

const sitePath = process.cwd();

async function compileAssets() {
	console.time("Assets Compilation Time");

	try {
		// Execute preassets hook
		await scriptHooks.preassets();

		// Get the current site config
		const config = require(`${sitePath}/config`);

		// Check for production flag
		const production = process.argv.includes("--prod");

		// Process each target in config
		for (const [target, files] of Object.entries(config.assets_compile)) {
			try {
				// Merge files
				let mergedContent = await mergePaths(files);

				// Process based on file type
				if (target.endsWith(".css")) {
					mergedContent = await processCss(mergedContent, production);
				} else if (target.endsWith(".js")) {
					mergedContent = await processJs(mergedContent, production);
				}

				// Write the final file
				await fs.writeFile(target, mergedContent);
				console.log(`Successfully compiled: ${target}`);
			} catch (error) {
				console.error(`Error processing ${target}:`, error);
				// Continue with other targets even if one fails
			}
		}

		// Execute postassets hook
		await scriptHooks.postassets();
	} catch (error) {
		console.error("Fatal error during compilation:", error);
		process.exit(1);
	} finally {
		console.timeEnd("Assets Compilation Time");
	}
}

async function mergePaths(files) {
	const contentPromises = [];

	for (const filePath of files) {
		if (filePath.includes("*")) {
			try {
				// Handle glob patterns - glob is now async by default
				const globPaths = await glob(filePath);
				const sortedPaths = globPaths.sort((a, b) =>
					path.basename(a).localeCompare(path.basename(b))
				);

				for (const globPath of sortedPaths) {
					contentPromises.push(readFileWithMetadata(globPath));
				}
			} catch (error) {
				console.error(`Error processing glob pattern ${filePath}:`, error);
				throw error;
			}
		} else {
			// Handle direct file paths
			contentPromises.push(readFileWithMetadata(filePath));
		}
	}

	const contents = await Promise.all(contentPromises);
	// Filter out empty JS files before joining
	const validContents = contents.filter(({ content, filePath }) => {
		if (!filePath.endsWith(".js")) return true;
		const trimmedContent = content.trim();
		return (
			trimmedContent !== "" &&
			trimmedContent !== "// You sure you are proficient in JavaScript?"
		);
	});

	return validContents
		.map(({ content, filePath }) => `\n/* File: ${filePath} */\n${content}`)
		.join("\n");
}

async function readFileWithMetadata(filePath) {
	try {
		const content = await fs.readFile(filePath, "utf8");
		return {
			content,
			filePath: path.relative(process.cwd(), filePath),
		};
	} catch (error) {
		console.error(`Error reading file ${filePath}:`, error);
		throw error;
	}
}

async function processCss(content, production) {
	try {
		// Replace node_modules paths
		let processedContent = content.replace(
			/..\/..\/node_modules/g,
			"node_modules"
		);

		// Compile SASS
		processedContent = sass
			.renderSync({
				data: processedContent,
				includePaths: ["assets/scss"],
			})
			.css.toString();

		// Minify in production
		if (production) {
			processedContent = new cleanCss().minify(processedContent).styles;
		}

		return processedContent;
	} catch (error) {
		console.error("Error processing CSS:", error);
		throw error;
	}
}

async function processJs(content, production) {
	if (!production) {
		return content;
	}

	try {
		const minified = uglifyJs.minify(content);
		if (minified.error) {
			throw minified.error;
		}
		return minified.code;
	} catch (error) {
		console.error("Error minifying JavaScript:", error);
		throw error;
	}
}

// Export for use as a module
module.exports = compileAssets;

// Run directly if this is the main module
if (require.main === module) {
	compileAssets().catch(console.error);
}
