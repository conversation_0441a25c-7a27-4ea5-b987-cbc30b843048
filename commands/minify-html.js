// Required NodeJS Modules
// =======================
var fs = require("fs");
var path = require("path");
var recursive = require("recursive-readdir");
var minify = require("html-minifier").minify;
var buildPath = `${process.cwd()}/build`;
const group_size = 13; // Number of pages being minified at the same time

// Get the current site config
const sitePath = process.cwd();
const config = require(`${sitePath}/config`);

// Error tracking
// ==============
var report_content = "";
var report_attachments = [];

// Minify a groupd of pages
// ===========================
var minifyGroup = function (files, index) {
	var pages_to_minify = [];
	var start_at = index * group_size; // Start index
	var end_at = (index + 1) * group_size; // End index
	end_at = end_at < files.length ? end_at : files.length;

	// Starting to minify all the pages in this range
	for (var i = start_at; i < end_at; i++) {
		var filepath = files[i];
		var extension = path.parse(filepath).ext.substr(1);
		if (extension == "html" && filepath.indexOf("/assets/") == -1) {
			// Update the current page global variable
			var page_promise = new Promise(function (resolve, reject) {
				// Get file informations
				minifyPage(filepath, resolve, reject);
			}).catch((error) =>
				process.stderr.write(
					`============ ### error in ${page.slug} ============\n\n${error}\n\n============ ### End of Error in ${page.slug} ============\n\n\n`
				)
			);
			pages_to_minify.push(page_promise);
		}
	}

	// Waiting for all the promises to resolve before minifying another group
	if (pages_to_minify.length) {
		Promise.all(pages_to_minify).then(function (results) {
			//console.log(`Done`)
			if (end_at < files.length) {
				minifyGroup(files, index + 1);
			} else {
				console.timeEnd("Minification Time");
			}
		});
	} else {
		if (end_at < files.length) {
			minifyGroup(files, index + 1);
		} else {
			console.timeEnd("Minification Time");
		}
	}
};

// Generate a minified page
// ===========================
var minifyPage = function (filepath, resolve, reject) {
	// Reading the original file
	fs.readFile(filepath, "utf8", function (error, contents) {
		if (!error) {
			try {
				// Compressing
				var result = minify(contents, {
					removeAttributeQuotes: true,
					collapseWhitespace: true,
					removeComments: true,
				});
				// Saving file
				fs.writeFile(filepath, result, function (error) {
					if (error) {
						console.info(`error minifying ${filepath}`);
					}
					// Skips to next page regardless the writing process was successful or not
					resolve();
				});
			} catch (error) {
				console.info(`error minifying ${filepath}`);
				// In case of error compressing the file, skipe to next page
				resolve();
			}
		} else {
			console.info(`error minifying ${filepath}`);
			// In case of error reading file, skips to next page
			resolve();
		}
	});
};

// Go each file in the _build folder & generate minified files
// ===========================================================
console.time("Minification Time");
recursive(buildPath, function (error, files) {
	// Minifying pages in async way in batches
	minifyGroup(files, 0);
});
