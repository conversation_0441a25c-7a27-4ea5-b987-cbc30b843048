console.log("\x1b[32mflash assets\x1b[0m\nCompile the assets.\n");
console.log("\x1b[32mflash build\x1b[0m\nBuilds a website.\n");
console.log(
	"\x1b[32mflash critical\x1b[0m\nGemerate critical CSS. Add --without-build to skip the build process.\n"
);
console.log(
	"\x1b[32mflash favicon\x1b[0m\nPlace an image named favicon.png in /assets/images/favicons/ and run this command to let flash generate all the favicon versions.\n"
);
console.log(
	"\x1b[32mflash fetch\x1b[0m\nFetch the data from the website API Provider. Add --modules-stories-limit to get just 40 entries per module.\n"
);
console.log(
	"\x1b[32mflash new [folder]\x1b[0m\nCreates a new website in the specified folder.\n"
);
console.log(
	'\x1b[32mflash new-block "block name"\x1b[0m\nCreates a new block.\n'
);
console.log(
	"\x1b[32mflash production-deploy\x1b[0m\nPrepare site for deployment.\n"
);
console.log(
	"\x1b[32mflash production-prepare\x1b[0m\nTest site for production. Add --no-critical to skip the critical css generation.\n"
);
console.log("\x1b[32mflash update\x1b[0m\nUpdate the Flash System.\n");
console.log(
	"\x1b[32mflash watch\x1b[0m\nHost a website locally on port 4000 and watch for changes.\n"
);
console.log(
	"\n\x1b[33mScript Hooks:\x1b[0m\nConfigure custom scripts in config.js under scriptHooks to run at specific lifecycle points:\nprefetch, postfetch, prebuild, postbuild, prewatch, preassets, postassets\n"
);
