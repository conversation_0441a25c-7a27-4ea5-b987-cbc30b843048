const express = require("express");
const cors = require("cors");
const { execSync } = require("child_process");
const sitePath = process.cwd();
const config = require(`${sitePath}/config`);
const ignore_critical_css = process.argv.indexOf("--no-critical") >= 0;

// Helper to log status with color codes
const log = (message, status = "info") => {
	const statusMap = {
		// info in white
		info: "\x1b[37m", // White
		success: "\x1b[32m", // Green
		error: "\x1b[31m", // Red
	};
	const color = statusMap[status] || "\x1b[34m";
	process.stdout.write(`${color}${message}\x1b[0m\n`);
};

// Function to execute a command and check for error keywords
const runCommandAndCheckOutput = (command, successMessage) => {
	try {
		const output = execSync(command).toString();
		if (output.toLowerCase().includes("error")) {
			log(`Error in command ${command}: ${output}`, "error");
			throw new Error(output);
		} else {
			log(output, "info");
			log(successMessage, "success");
		}
	} catch (error) {
		log(`Error executing ${command}: ${error.message}`, "error");
		throw error;
	}
};

// Process information
const processName = ignore_critical_css
	? "Production Prepare Procedure Without Critical CSS"
	: "Production Prepare Procedure With Critical CSS";

log(`\n${processName}`, "success");
log("============================", "success");
console.time(processName + " Time");

// Fetch content
log("\n\n=> Getting the space data", "success");
runCommandAndCheckOutput(
	"flash fetch --published",
	"\u2713 Space data fetched"
);

// Compiling and minifying assets
log("\n\n=> Compiling Assets", "success");
runCommandAndCheckOutput("flash assets --prod", "\u2713 Assets compiled");

// Production build
log("\n\n=> Building for Production", "success");
runCommandAndCheckOutput(
	"flash build --prod",
	"\u2713 Production build complete"
);

// Generating critical css (if not skipped)
if (!ignore_critical_css) {
	log("\n\n=> Generating Critical CSS", "success");
	console.time("Generating Critical CSS Time");
	const outputCritical = execSync("flash critical");
	log(outputCritical.toString(), "info");
	console.timeEnd("Generating Critical CSS Time");
	log("\u2713 Critical CSS generation finished", "success");
} else {
	log("\u2713 Critical CSS generation skipped", "success");
}

// Minifying html
log("\n\n=> Minifying HTML", "success");
const outputMinify = execSync("flash minify-html");
log(outputMinify.toString(), "info");
log("\u2713 Minifying HTML finished", "success");

// Starting app
const app = express();
app.use(cors());
app.use(express.static(config.build.folder));
app.get("*", (req, res) => {
	res.status(404).sendFile(`${sitePath}/build/404.html`);
});
app.listen(1337, () => {
	log(
		"\nCheck the production preview at http://localhost:1337/.\n\n\nPlease make sure that the website is working fine before deploying anything. (Press CTRL+C to exit the process)",
		"info"
	);
	console.log("\n\n");
	console.timeEnd(processName + " Time");

	log("\n\n=> Production Preview", "success");
});
