// Node Modules
const HeadlessFlash = require('./../system/headless-flash');
var environment = 'dev';
// Any specific build path?
var data;
process.argv.forEach((argument) => {
	if(argument.indexOf('--data=') >= 0) data = argument.replace('--data=', '');
});
// Build the website or just one page?
let buff = new Buffer(data, 'base64'); 
let entry_data = JSON.parse(decodeURI(buff.toString('ascii')));
var story = entry_data.story;
story.data = Object.assign({}, story.content);
delete story.content;
story.title = story.name;
if(story.path) {
  story.url = `/${story.path.replace(/\/$/, '').replace(/^\//, '')}/`;
} else {
  story.url = `/${story.full_slug.replace(/\/$/, '')}/`;
}
story.layout = 'default';
// Buliding the whole site
console.log(HeadlessFlash.buildEntryLive(story));
