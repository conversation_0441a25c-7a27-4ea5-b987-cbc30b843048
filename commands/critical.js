// Required NodeJS Modules
const fs = require("fs");
const path = require("path");
const critical = require("critical");
const recursive = require("recursive-readdir");
const puppeteer = require("puppeteer-core");

const sitePath = process.cwd();
const buildPath = `${sitePath}/build`;
const config = require(`${sitePath}/config`);
const utils = require("./../system/utils");
const pages = utils.getRawPages();
const { getChromePath } = require("chrome-launcher");

const chromePath = getChromePath();

// Delete Folder which is not empty
const deleteFolderRecursive = function (path) {
	if (fs.existsSync(path)) {
		fs.readdirSync(path).forEach(function (file) {
			const curPath = path + "/" + file;
			if (fs.lstatSync(curPath).isDirectory()) {
				deleteFolderRecursive(curPath);
			} else {
				fs.unlinkSync(curPath);
			}
		});
		fs.rmdirSync(path);
	}
};

// Clean the css critical folder
deleteFolderRecursive(`${sitePath}/criticalcss`);
fs.mkdirSync(`${sitePath}/criticalcss`);

// Generate critical CSS Files
const registered_components = [];
const generateCriticalCss = function (files, index) {
	if (files[index] === undefined) {
		console.log("Critical CSS generation completed.");
		return;
	}

	const filepath = files[index];
	const data_filepath = filepath.replace("/build/", "/pages/storyblok/");
	const extension = path.parse(filepath).ext.substr(1);

	const current_file = data_filepath.replace(process.cwd(), "");
	const page_data = pages.find(
		(page) =>
			current_file === `/pages/${config.storyblok.folder}${page.url}index.html`
	);
	const component = page_data ? page_data.data.component : null;

	if (
		extension !== "html" ||
		filepath.includes("/assets/") ||
		!component ||
		registered_components.includes(component)
	) {
		return generateCriticalCss(files, index + 1);
	}

	if (component !== "Page") {
		registered_components.push(component);
	}

	let css_filename =
		component === "Page"
			? filepath
					.replace(`${buildPath}/`, "")
					.replace("/index", "")
					.replace(/\//g, "-")
					.replace("." + extension, "")
			: `${page_data.data.component}`;

	css_filename = css_filename === "index" ? "homepage" : css_filename;
	css_filename = css_filename
		.toLowerCase()
		.trim()
		.replace(/\s+/g, "-")
		.replace(/&/g, "-and-")
		.replace(/[^\w\-]+/g, "")
		.replace(/\-\-+/g, "-");

	console.log(`=> Processing ${filepath}...`);

	const include = config.criticalcss?.include || [];
	const ignore = config.criticalcss?.ignore || [];

	critical.generate(
		{
			base: buildPath,
			src: filepath.replace(`${buildPath}/`, ""),
			target: {
				css: `${sitePath}/criticalcss/${css_filename}.css`,
			},
			minify: true,
			width: 1300,
			height: 1100,
			include: include,
			ignore: {
				atrule: ["@font-face"],
				...ignore,
			},
			penthouse: {
				puppeteer: {
					getBrowser: async () => {
						return puppeteer.launch({
							executablePath: chromePath ?? process.env["CHROMIUM_PATH"],
							ignoreHTTPSErrors: true,
							headless: "new",
							args: [
								"--no-sandbox",
								"--disable-setuid-sandbox",
								"--disable-gpu",
								"--disable-extensions",
								"--disable-background-networking",
								"--disable-sync",
								"--disable-background-timer-throttling",
								"--disable-breakpad",
								"--disable-client-side-phishing-detection",
								"--disable-default-apps",
								"--disable-hang-monitor",
								"--disable-popup-blocking",
								"--disable-translate",
								"--disable-infobars",
								"--metrics-recording-only",
								"--mute-audio",
								"--no-first-run",
								"--no-default-browser-check",
								"--disable-dev-shm-usage",
								"--headless",
								`--window-size=${1200},${900}`,
							],
						});
					},
				},
			},
		},
		(err, output) => {
			if (err) {
				console.error(
					`Error generating critical CSS for ${css_filename}:`,
					err
				);
			}
			generateCriticalCss(files, index + 1);
		}
	);
};

// Start the critical CSS generation process
recursive(buildPath, (err, files) => {
	if (err) {
		console.error("Error reading build directory:", err);
		return;
	}
	generateCriticalCss(files, 0);
});
