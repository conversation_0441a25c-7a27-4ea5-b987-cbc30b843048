const fs = require("fs-extra");
const { execSync } = require("child_process");
const path = require("path");

function findGitRoot(startPath) {
	let currentPath = startPath;
	while (currentPath !== path.parse(currentPath).root) {
		if (fs.existsSync(path.join(currentPath, ".git"))) {
			return currentPath;
		}
		currentPath = path.dirname(currentPath);
	}
	throw new Error("Git root directory not found");
}

function updateProject() {
	try {
		// Get current file's directory
		const currentFilePath = path.dirname(fs.realpathSync(__filename));
		console.log("Current path:", currentFilePath);

		// Find git root directory
		const projectRoot = findGitRoot(currentFilePath);
		console.log("Project root:", projectRoot);

		// Change to project root directory and execute commands
		process.chdir(projectRoot);

		console.log("Pulling latest changes...");
		execSync("git pull", { stdio: "inherit" });

		console.log("Installing dependencies...");
		execSync("npm install", { stdio: "inherit" });

		console.log("Update completed successfully! 🎉");
	} catch (error) {
		console.error("Error during update:", error.message);
		process.exit(1);
	}
}

updateProject();
