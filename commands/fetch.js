// NODE MODULES
// ==========================
const utils = require("./../system/utils");
const StoryblokClient = require("storyblok-js-client");
const fse = require("fs-extra");
const sitePath = process.cwd();
const config = require(`${sitePath}/config`);
const moment = require("moment");
const scriptHooks = require("./../system/script-hooks");
const published = process.argv.indexOf("--published") >= 0;
const api_version = published ? "published" : "draft";

// SCRIPT CONFIGURATION VARIABLES
// ==========================
let partial_mode = false;
let partial_mode_limit = 40;
let partial_stories = [];
let entry_id = null;
const api_page_size = 80;

// STORYBLOK CLIENT SETUP
//  ======================
const Storyblok = new StoryblokClient({
	accessToken: config.storyblok.api_token,
	cache: {
		clear: "auto",
		type: "memory",
	},
});

// GETTING ARGS
// ==========================
process.argv.forEach((arg) => {
	if (arg.includes("--modules-stories-limit")) {
		partial_mode = true;
		let partial_mode_parameter = arg.split("=");
		partial_mode_limit =
			partial_mode_parameter.length == 1
				? partial_mode_limit
				: partial_mode_parameter[1];
	}
	if (arg.indexOf("--entry-id=") >= 0) {
		entry_id = arg.replace("--entry-id=", "");
	}
});

// METHODS
// ===================================
/**
 * Get all the stories from Storyblok
 */
async function getStories() {
	// Start timing the download of stories
	console.time("Download Stories Time");

	// Request options for Storyblok
	let request_options = {
		query: {
			version: api_version,
			per_page: api_page_size,
		},
	};

	// Getting the data
	let pages = await getData("stories", "stories", request_options);

	// End timing the download of stories
	console.timeEnd("Download Stories Time");

	if (!pages.data || pages.error) {
		throw new Error("error: no stories retrieved");
	}
	let stories_list = pages.data;

	// Calling the callback
	return await getStoriesCallback(stories_list);
}

/**
 * Get all data from Storyblok API.
 * @param {string} endpoint The endpoint to query.
 * @param {string} entity_name The name of the entity to be retrieved from the API response.
 * @param {object} [params] Parameters to add to the API request.
 * @return {Promise} The data fetched from the API.
 */
async function getData(endpoint, entity_name, params) {
	return new Promise(async (resolve) => {
		let data = [];
		let data_requests = [];

		// Fetch the first page with minimal data to get total entries
		let initial_params = JSON.parse(JSON.stringify(params)); // Deep copy
		initial_params.query.per_page = 1;
		initial_params.query.page = 1;

		let first_page;
		try {
			first_page = await apiRequest(endpoint, initial_params);
		} catch (err) {
			resolve({ error: true, message: err });
			return;
		}

		if (!first_page.data) {
			resolve({ error: true });
			return;
		}

		// Get total entries and calculate total pages
		let total_entries = parseInt(first_page.headers.total, 10);
		let total_pages = Math.ceil(total_entries / api_page_size);

		if (published) {
			process.stdout.write(
				`Getting ${total_pages} pages of ${api_page_size} entries each\n`
			);
		}

		// Prepare requests for all pages including the first page
		for (let page_index = 1; page_index <= total_pages; page_index++) {
			let page_params = JSON.parse(JSON.stringify(params)); // Deep copy
			page_params.query.page = page_index;
			data_requests.push(apiRequest(endpoint, page_params));
		}

		// Fetch all pages in parallel
		Promise.all(data_requests)
			.then((values) => {
				values.forEach((response) => {
					if (response.data) {
						data = data.concat(response.data[entity_name]);
					}
				});
				resolve({ data: data });
			})
			.catch((err) => {
				resolve({ error: true, message: err });
			});
	});
}

/**
 * Get a page of data from Storyblok API.
 * @param {string} endpoint The endpoint to query.
 * @param {object} [params] Parameters to add to the API request.
 * @return {Promise} The data fetched from the API.
 */
async function apiRequest(endpoint, params) {
	// Storyblok query options
	let request_options = {};
	// Adding the optional query filters
	if (params && params.query) {
		Object.assign(request_options, params.query);
	}
	// API request
	let retries = 0;
	// Method to submit the request and eventually retry
	function submitRequest() {
		return new Promise((resolve) => {
			Storyblok.get(`cdn/${endpoint}`, request_options)
				.then((response) => {
					// Returning the response from the endpoint
					resolve(response);
				})
				.catch((err) => {
					// Error handling
					process.stdout.write(err.message);
					if (retries < 4) {
						retries++;
						resolve(submitRequest());
					} else {
						process.stderr.write("ERROR FETCHING PAGE OF STORIES");
						process.stderr.write(err);
						try {
							switch (err.response.status) {
								case 401:
									process.stderr.write(
										"\x1b[31mStoryblokTo11ty - Error 401: Unauthorized. Probably the API token is wrong.\x1b[0m"
									);
									break;
								case 404:
									process.stderr.write(
										"\x1b[31mStoryblokTo11ty - Error 404: The item you are trying to get doesn't exist.\x1b[0m"
									);
									break;
								default:
									process.stderr.write(
										`\x1b[31mStoryblokTo11ty - Error ${err.response.status}: ${err.response.statusText}`
									);
									break;
							}
						} catch (e) {
							process.stderr.write(e);
						}
						resolve({ data: null });
					}
				});
		});
	}

	return await submitRequest();
}

/**
 * Get a single story from Storyblok
 * @param {string} entry_id The id of the entry to retrieve
 * @return {Promise} The data fetched from the API.
 */
async function getStory(entry_id) {
	try {
		let response = await Storyblok.get(`cdn/stories/${entry_id}`, {
			version: api_version,
		});
		let story = response.data.story;
		if (story) {
			utils.getPagesModules();
			utils.pages_modules[entry_id] = story.content.component;
			let sitemap = utils.sitemapRecursive(0, Object.values(links));
			await fse.writeJson("data/sitemap.json", sitemap);
			await saveStory(story);
			console.log("Done");
		}
	} catch (error) {
		console.error("Error fetching story with ID:", entry_id);
		console.error("API Version:", api_version);

		if (error.response) {
			console.error("Response Status:", error.response.status);
			console.error("Response Status Text:", error.response.statusText);
			console.error("Response Data:", error.response.data);
		} else if (error.request) {
			console.error("No response received from server");
			console.error("Request details:", error.request);
		} else {
			console.error("Error setting up request:", error.message);
		}

		console.error("Full error object:", error);
		process.stderr.write(
			`Error fetching story: ${error.message || "Unknown error"}`
		);
	}
}

/**
 * Save a single story fetched, changing the object structure
 * @param {object} story The story object
 * @return {object} The transformed story
 */
async function saveStory(story) {
	story.title = story.name;
	if (story.path) {
		// Overriding the story url with the path
		story.url = `/${story.path.replace(/\/$/, "").replace(/^\//, "")}/`;
	} else {
		// Building the story url
		let url_parts = [];
		story.full_slug.split("/").forEach(function (part) {
			url_parts.push(part.substr(0, 140));
		});
		story.url = `${config.root_path || "/"}${url_parts
			.join("/")
			.replace(/\/$/, "")}/`;
	}
	story.original_url = `${config.root_path || "/"}${story.full_slug.replace(
		/\/$/,
		""
	)}/`;
	// Check if it's the homepage
	let is_homepage = story.full_slug == "homepage";
	// Appending additional data
	story.layout = "default";
	story.data = story.content;
	story.fetch_timestamp = timestamp;
	story.position =
		pagesPositions[story.id] !== undefined ? pagesPositions[story.id] : 0;
	// Parent position if it's a starting page
	if (story.parent_id && story.is_startpage) {
		story.parent_position = pagesPositions[story.parent_id];
	}
	// Removing Storyblok properties
	delete story.name;
	delete story.full_slug;
	delete story.content;

	// Settings Page
	if (story.url == `${config.root_path || "/"}settings/`) {
		// URL replacement of assets if set
		if (config.assets_url_masking && config.force_imgix) {
			story.data = JSON.parse(
				JSON.stringify(story.data).replace(
					/s3\.amazonaws\.com\/a\.storyblok\.com|a\.storyblok\.com/g,
					config.assets_url_masking
				)
			);
		}
		await fse.writeJson("data/settings.json", story.data);
	}
	// Normal Page
	else {
		await utils.createPage(
			story,
			"",
			`${config.storyblok.folder}${utils.getPagePath(story)}index.html`
		);
		// If it's the homepage, create a duplicate of the entry in the root directory
		if (is_homepage) {
			story.url = `${config.root_path || "/"}`;
			await utils.createPage(
				story,
				"",
				`${config.storyblok.folder}/index.html`
			);
		}
	}
	return story;
}

/**
 * Actions to execute when the getStoriesCallback function has done its stuff
 */
async function afterCallbackActions() {
	try {
		await scriptHooks.postfetch();
	} catch (error) {
		console.error("Warning: postfetch hook failed:", error.message);
	}
	console.log("Done");
	console.timeEnd("Total Script Time");
}

/**
 * Actions to perform when all stories have been fetched
 */
async function getStoriesCallback(stories_list) {
	// Start timing the processing of stories
	console.time("Process Stories Time");

	// Empty the Storyblok folder
	await fse.emptyDir(`pages/${config.storyblok.folder}`);

	// Get all the pages of the Storyblok space and create them
	let stories = stories_list.filter((story) =>
		links.hasOwnProperty(story.uuid)
	);
	let modules_count = {};
	process.stdout.write(`${stories_list.length} Stories Fetched \n`);

	// Process stories in parallel
	await Promise.all(
		stories.map(async (story) => {
			// Pages filter for partial mode
			if (
				partial_mode &&
				story.content &&
				story.content.component &&
				!["Page", "Standard Page"].includes(story.content.component)
			) {
				// If we have more than the limit, skip the story
				if (
					modules_count[story.content.component] &&
					modules_count[story.content.component] > partial_mode_limit
				) {
					return;
				} else {
					// Increase the counter
					modules_count[story.content.component] =
						(modules_count[story.content.component] || 0) + 1;
				}
			}
			// Save Story in a file
			await saveStory(story);
			// Add entry to the partial stories array
			if (partial_mode) {
				partial_stories.push(story);
			}
		})
	);

	// End timing the processing of stories
	console.timeEnd("Process Stories Time");

	// Generating the sitemap
	// Start timing the sitemap generation
	console.time("Sitemap Generation Time");

	// If it's the partial mode, get rid of the unused links
	if (partial_mode) {
		let partial_links = {};
		for (let uuid in links) {
			if (
				partial_stories.find((story) => story.uuid == uuid) ||
				links[uuid].is_folder
			) {
				partial_links[uuid] = Object.assign({}, links[uuid]);
			}
		}
		links = Object.assign({}, partial_links);
	}
	let sitemap = utils.sitemapRecursive(0, Object.values(links), stories);
	await fse.writeJson("data/sitemap.json", sitemap);

	// End timing the sitemap generation
	console.timeEnd("Sitemap Generation Time");

	// GETTING DATASOURCES
	// ================================
	let fetch_datasources =
		config.datasources || config.storyblok.datasources || [];
	if (fetch_datasources && fetch_datasources.length) {
		// Start timing the datasource fetching
		console.time("Datasources Fetch Time");
		try {
			let datasources = await Promise.all(
				fetch_datasources.map((datasource) => fetchDatasources(datasource))
			);
			fetch_datasources.forEach((datasource, index) => {
				if (datasources && datasources[index]) {
					fse.writeJson(`datasources/${datasource}.json`, datasources[index]);
				}
			});
			// End timing the datasource fetching
			console.timeEnd("Datasources Fetch Time");
			afterCallbackActions();
		} catch (e) {
			process.stderr.write("Datasource Errors: " + e);
			console.timeEnd("Datasources Fetch Time");
			afterCallbackActions();
		}
	} else {
		afterCallbackActions();
	}
}

/**
 * Fetch datasources or a single datasource
 * @param {string} datasource The datasource name
 * @return {array} The array of datasource entries
 */
async function fetchDatasources(datasource) {
	try {
		// Fetch the first page to get total entries
		let initial_response = await fetchDatasourcePage(datasource, 1);
		let total_entries = parseInt(initial_response.total, 10);
		let total_pages = Math.ceil(total_entries / 100); // Assuming per_page is 100

		// Prepare requests for all pages
		let data_requests = [];
		for (let page = 1; page <= total_pages; page++) {
			data_requests.push(fetchDatasourcePage(datasource, page));
		}

		// Fetch all pages in parallel
		let pages = await Promise.all(data_requests);
		let datasources = pages.reduce(
			(acc, pageData) => acc.concat(pageData.entries),
			[]
		);
		console.log(`Datasource "${datasource}" downloaded successfully`);
		return datasources;
	} catch (error) {
		console.error(
			`Error while trying to fetch the datasource called ${datasource}:`
		);
		console.error("API Version:", api_version);

		if (error.response) {
			console.error("Response Status:", error.response.status);
			console.error("Response Status Text:", error.response.statusText);
			console.error("Response Data:", error.response.data);
		} else if (error.request) {
			console.error("No response received from server");
			console.error("Request details:", error.request);
		} else {
			console.error("Error setting up request:", error.message);
		}

		console.error("Full error object:", error);
		return [];
	}
}

/**
 * Fetch a single page of a datasource
 * @param {string} datasource The datasource name
 * @param {int} page The number of the page
 * @return {Promise} The data fetched from the API.
 */
function fetchDatasourcePage(datasource, page) {
	return new Promise((resolve, reject) => {
		Storyblok.get("cdn/datasource_entries", {
			version: api_version,
			datasource: datasource,
			per_page: 100,
			page: page,
		})
			.then((response) => {
				resolve({
					entries: response.data.datasource_entries,
					total: response.total || response.headers.total,
				});
			})
			.catch((err) => {
				console.error(
					`Error fetching datasource page ${page} for ${datasource}:`
				);
				console.error("API Version:", api_version);

				if (err.response) {
					console.error("Response Status:", err.response.status);
					console.error("Response Status Text:", err.response.statusText);
					console.error("Response Data:", err.response.data);
				} else if (err.request) {
					console.error("No response received from server");
					console.error("Request details:", err.request);
				} else {
					console.error("Error setting up request:", err.message);
				}

				console.error("Full error object:", err);
				reject(err);
			});
	});
}

/**
 * Fetch the current CV from Storyblok
 * @return {Promise<number|null>} The current CV or null if there's an error
 */
async function getCurrentCV() {
	try {
		const response = await Storyblok.get("cdn/spaces/me", {
			version: api_version,
		});
		return response.data.space.version;
	} catch (error) {
		console.error("Error fetching current CV from Storyblok API:");
		console.error("API Endpoint: cdn/spaces/me");
		console.error("API Version:", api_version);

		if (error.response) {
			console.error("Response Status:", error.response.status);
			console.error("Response Status Text:", error.response.statusText);
			console.error("Response Data:", error.response.data);
		} else if (error.request) {
			console.error("No response received from server");
			console.error("Request details:", error.request);
		} else {
			console.error("Error setting up request:", error.message);
		}

		console.error("Full error object:", error);
		return null;
	}
}

// SCRIPT EXECUTION
// ====================================

// Start timing the total script execution
console.time("Total Script Time");

let pagesPositions = {};
let links = {};
let pages_number = 0;
let timestamp = moment().unix();

// Execute prefetch hook
(async () => {
	try {
		await scriptHooks.prefetch();
	} catch (error) {
		console.error("Warning: prefetch hook failed:", error.message);
	}

	// Make sure to create the data folder first
	fse.ensureDirSync("data");
	fse.ensureDirSync("datasources");

	// Output download message
	process.stdout.write(`\nChecking for updates from Storyblok\n`);

	// Starting fetch timer
	console.time("Fetch Time");

	// If the Storyblok folder is defined in the config, the script keeps going
	if (config.storyblok !== undefined) {
		// Start timing the download of CV
		console.time("Download CV Time");

		// Fetch the current CV from Storyblok
		getCurrentCV()
			.then(async (currentCV) => {
				// End timing the download of CV
				console.timeEnd("Download CV Time");

				if (currentCV === null) {
					throw new Error("Error fetching current CV");
				}

				// Read the stored CV from local file
				let storedCV = null;
				const cvPublished = `data/published-cv.json`;
				const cvDraft = `data/draft-cv.json`;
				const cvFilePath = published ? cvPublished : cvDraft;
				const deleteCvPath = published ? cvDraft : cvPublished;

				// Delete the other CV file if it exists
				if (fse.existsSync(deleteCvPath)) {
					fse.unlinkSync(deleteCvPath);
				}

				if (fse.existsSync(cvFilePath)) {
					try {
						const cvData = await fse.readFile(cvFilePath, "utf8");
						storedCV = parseInt(cvData, 10);
					} catch (error) {
						console.error("Error reading stored CV:", error);
					}
				}

				// Check if Storyblok folder exists and has content
				const storyblokFolderPath = `pages/${config.storyblok.folder}`;
				let storyblokFolderEmpty = false;

				try {
					if (!fse.existsSync(storyblokFolderPath)) {
						storyblokFolderEmpty = true;
						console.log("Storyblok folder does not exist. Will fetch content.");
					} else {
						const files = await fse.readdir(storyblokFolderPath);
						if (!files || files.length === 0) {
							storyblokFolderEmpty = true;
							console.log("Storyblok folder is empty. Will fetch content.");
						}
					}
				} catch (error) {
					storyblokFolderEmpty = true;
					console.log("Error checking Storyblok folder. Will fetch content.");
				}

				// Compare CVs - but force fetch if Storyblok folder is empty
				if (storedCV === currentCV && !storyblokFolderEmpty) {
					console.log("CV has not changed. No updates needed.");
					console.timeEnd("Total Script Time");
					return;
				}

				if (storyblokFolderEmpty) {
					console.log("Forcing content fetch due to empty Storyblok folder.");
				}

				// Proceed to fetch links and stories
				console.time("Download Links Time");

				// Getting the list of links to build the sitemap
				Storyblok.get("cdn/links", {
					version: api_version,
				})
					.then(async (response) => {
						// End timing the download of links
						console.timeEnd("Download Links Time");

						// Get all the pages positions in Storyblok
						let all_links = response.data.links;

						// Getting the pages positions
						for (let i in all_links) {
							let link = all_links[i];
							pagesPositions[link.id] = link.position;
							links[i] = link;
						}

						// If the fetch action is just for one story
						if (entry_id) {
							return await getStory(entry_id);
						}

						// If we want to retrieve multiple stories
						// calculate the number of pages to request
						pages_number = Math.ceil(Object.keys(links).length / api_page_size);
						await fse.emptyDir(`pages/${config.storyblok.folder}`);
						return await getStories();
					})
					.catch((error) => {
						console.error("Error while retrieving links from Storyblok API:");
						console.error("API Endpoint: cdn/links");
						console.error("API Version:", api_version);

						if (error.response) {
							// The request was made and the server responded with a status code
							// that falls out of the range of 2xx
							console.error("Response Status:", error.response.status);
							console.error("Response Status Text:", error.response.statusText);
							console.error("Response Headers:", error.response.headers);
							console.error("Response Data:", error.response.data);
						} else if (error.request) {
							// The request was made but no response was received
							console.error("No response received from server");
							console.error("Request details:", error.request);
						} else {
							// Something happened in setting up the request that triggered an Error
							console.error("Error setting up request:", error.message);
						}

						console.error("Full error object:", error);
						throw new Error(
							`Error while retrieving links: ${
								error.message || "Unknown error"
							}`
						);
					});

				// Update stored CV
				try {
					await fse.writeFile(cvFilePath, currentCV.toString());
				} catch (error) {
					console.error("Error writing new CV:", error);
				}
			})
			.catch((error) => {
				console.error("Error in fetching CV and comparing:", error);
			});
	} else {
		process.stdout.write("Storyblok folder is not defined");
	}
})();
