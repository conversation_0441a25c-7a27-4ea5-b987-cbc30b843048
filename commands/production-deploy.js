const { execSync } = require("child_process");

// Helper to log status with color codes
const log = (message, status = "info") => {
	const statusMap = {
		info: "\x1b[37m", // White
		success: "\x1b[32m", // Green
		error: "\x1b[31m", // Red
	};
	const color = statusMap[status] || "\x1b[34m";
	process.stdout.write(`${color}${message}\x1b[0m\n`);
};

// Function to execute a command and check for error keywords
const runCommandAndCheckOutput = (command, successMessage) => {
	try {
		const output = execSync(command).toString();
		if (output.toLowerCase().includes("error")) {
			log(`Error in command ${command}: ${output}`, "error");
			throw new Error(output);
		} else {
			log(output, "info");
			log(successMessage, "success");
		}
	} catch (error) {
		log(`Error executing ${command}: ${error.message}`, "error");
		throw error;
	}
};

const processName = "Production Deploy Procedure";

log(`\n${processName}`, "success");
log("============================", "success");
console.time(`${processName} Time`);

// Fetch content
log("\n\n=> Getting the space data", "success");
runCommandAndCheckOutput(
	"flash fetch --published",
	"\u2713 Space data fetched"
);

// Compiling and minifying assets
log("\n\n=> Compiling Assets", "success");
runCommandAndCheckOutput("flash assets --prod", "\u2713 Assets compiled");

// Production build
log("\n\n=> Building for Production", "success");
runCommandAndCheckOutput(
	"flash build --prod",
	"\u2713 Production build complete"
);

// Generating critical css
log("\n\n=> Generating Critical CSS", "success");
console.time("Generating Critical CSS Time");
const outputCritical = execSync("flash critical");
log(outputCritical.toString(), "info");
console.timeEnd("Generating Critical CSS Time");
log("\u2713 Critical CSS generation finished", "success");

// Minifying html
log("\n\n=> Minifying HTML", "success");
const outputMinify = execSync("flash minify-html");
log(outputMinify.toString(), "info");
log("\u2713 Minifying HTML finished", "success");

console.timeEnd(`${processName} Time`);

log("\n\n=> Production deploy Complete", "success");
