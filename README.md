# Flash 2024: Static Website Generator with Storyblok Integration

## Overview

Flash 2024 is a powerful CLI tool and framework designed to build static websites using Storyblok as a content management system (CMS). It provides a streamlined workflow for developers to fetch content from Storyblok, build static HTML pages, and deploy them to production environments.

The framework follows a component-based architecture where Storyblok components are mapped to local template files, allowing developers to create modular and reusable UI components.

## Key Features

- **Storyblok Integration**: Seamlessly fetches content from Storyblok CMS
- **Component-Based Architecture**: Maps Storyblok components to local templates
- **Static Site Generation**: Builds optimized static HTML pages
- **Development Workflow**: Provides live reloading during development
- **Asset Optimization**: Compiles and minifies CSS, JavaScript, and images
- **Critical CSS**: Generates critical CSS for improved page load performance (working very poorly, needs several improvements)
- **Production Deployment**: Prepares and deploys sites to production environments

## Installation

1. Clone the repository:

   ```bash
   git clone [repository-url]
   ```

2. Use the correct Node.js version:

   ```bash
   nvm use
   ```

3. Install dependencies:

   ```bash
   npm ci
   ```

4. Link the CLI globally:
   ```bash
   npm link --force
   ```

## Configuration

Create a `config.js` file in your project root with the following structure:

```javascript
module.exports = {
	// Storyblok configuration
	storyblok: {
		api_token: "YOUR_STORYBLOK_API_TOKEN",
		folder: "storyblok", // Folder where Storyblok pages will be stored
	},

	// Build configuration
	build: {
		folder: "./build", // Output folder for the built site
	},

	// Watch configuration for development
	watch: {
		build: ["./pages/**/*.html", "./blocks/**/*.html"],
		assets: ["./assets/**/*"],
	},

	// Modules that don't need detail pages
	modules_without_detail: [],

	// Script hooks - custom scripts to run at specific lifecycle points
	scriptHooks: {
		prefetch: 'echo "Running prefetch hook"',
		postfetch: 'echo "Running postfetch hook"',
		prebuild: 'echo "Running prebuild hook"',
		postbuild: 'echo "Running postbuild hook"',
		prewatch: 'echo "Running prewatch hook"',
		preassets: 'echo "Running preassets hook"',
		postassets: 'echo "Running postassets hook"',
	},
};
```

## CLI Commands

Flash 2024 provides several commands to help you build and manage your static website:

- `flash help`: Displays all available commands
- `flash fetch`: Fetches content from Storyblok
- `flash build`: Builds the static website
- `flash watch`: Starts a development server with live reloading
- `flash assets`: Compiles and optimizes assets (CSS, JS, images)
- `flash critical`: Generates critical CSS
- `flash new-block "Block Name"`: Creates a new block template
- `flash production-prepare`: Prepares the site for production
- `flash production-deploy`: Deploys the site to production

### Command Options

- `flash fetch --published`: Fetches only published content
- `flash fetch --modules-stories-limit=40`: Limits the number of stories fetched per module
- `flash fetch --entry-id=123456`: Fetches a specific entry by ID
- `flash build --prod`: Builds the site in production mode
- `flash build --file=path/to/file.html`: Builds a specific file
- `flash build --entry-id=123456`: Builds pages for a specific entry
- `flash watch --port=8080`: Starts the development server on a specific port

## Project Structure

A typical Flash 2024 project has the following structure:

```
project/
├── assets/              # Source assets (CSS, JS, images)
├── blocks/              # Block templates
│   ├── block/           # Block category
│   │   └── component/   # Component template
│   │       ├── view.html
│   │       ├── style.scss
│   │       └── script.js
├── pages/               # Page templates
│   └── storyblok/       # Storyblok pages
├── data/                # Data files (JSON)
├── build/               # Built static site
└── config.js            # Configuration file
```

## Component Mapping System

### Naming Convention

Flash 2024 uses a specific naming convention to map Storyblok components to local template files:

**General Rule**: Storyblok components follow the naming convention `[Category] Name`. The "Category" maps to a subdirectory within `blocks/` (e.g., `[Block]` -> `blocks/block/`). The "Name" is converted to a slug, and the "Category\_" prefix is removed. The final path is `blocks/category/slug/view.html`.

For example:

- Storyblok component: `[Block] Hero`
- Local template path: `blocks/block/hero/view.html`

**Module Exception**: If a component name ends with " Module", it's treated differently. The " Module" suffix is replaced with " Detail", and the category is changed to "[Block]". Then, the general rule is applied.

For example:

- Storyblok component: `[Feature] Courses Module`
- Transformed to: `[Block] Courses Detail`
- Local template path: `blocks/block/courses_detail/view.html`

### Creating Components

To create a new component:

1. Create a new component in Storyblok following the naming convention `[Category] Name`
2. Run `flash new-block "Block Name"` or manually create the directory structure:

   ```
   blocks/category/name/
   ├── view.html
   ├── style.scss
   └── script.js
   ```

3. Edit the `view.html` file to define your component's HTML structure:

   ```html
   <% var block = options.block %> <%- block._editable %>

   <div class="component-name">
   	<div class="container">
   		<!-- Your HTML Code Here -->
   		<h2><%= block.headline %></h2>
   		<p><%= block.text %></p>
   	</div>
   </div>
   ```

## Development Workflow

1. **Setup Storyblok**: Create your content structure in Storyblok
2. **Fetch Content**: Run `flash fetch` to get content from Storyblok
3. **Create Components**: Create block templates for your Storyblok components
4. **Development**: Run `flash watch` to start the development server
5. **Build**: Run `flash build --prod` to build the production-ready site
6. **Deploy**: Run `flash production-deploy` to deploy your site

## Using Plugins

Flash 2024 comes with several built-in plugins that provide additional functionality:

### Stories Plugin

The `stories` plugin allows you to fetch and display lists of content from Storyblok:

```html
<% plugins.stories({ component: 'News Module', limit: 10, sort: 'desc',
order_by: 'created_at', paginate: true, categories: ['news'] }, (news) => { %>
<div class="news-item">
	<h3><%= news.title %></h3>
	<p><%= news.data.excerpt %></p>
	<a href="<%= news.url %>">Read More</a>
</div>
<% }, () => { %>
<p>No news found.</p>
<% }) %>
```

### Blocks Plugin

The `blocks` plugin renders Storyblok components:

```html
<%= plugins.blocks(page.data.body) %>
```

### Other Useful Plugins

- `imgix`: Optimizes images using Imgix
- `richText`: Renders rich text content
- `markdownify`: Converts Markdown to HTML
- `wysiwyg`: Renders WYSIWYG content
- `toc`: Generates a table of contents
- `debugData`: Displays debug information for development

## Script Hooks

Flash 2024 supports script hooks that allow you to run custom commands at specific points in the build lifecycle. This is useful for tasks like:

- Running linters or code formatters before builds
- Syncing files or databases before fetching content
- Deploying assets to CDNs after compilation
- Running tests after builds
- Custom preprocessing or postprocessing tasks

### Available Hooks

- **`prefetch`**: Runs before `flash fetch` command
- **`postfetch`**: Runs after `flash fetch` command completes
- **`prebuild`**: Runs before `flash build` command
- **`postbuild`**: Runs after `flash build` command completes
- **`prewatch`**: Runs before `flash watch` starts the development server
- **`preassets`**: Runs before `flash assets` compiles assets
- **`postassets`**: Runs after `flash assets` completes

### Configuration

Add script hooks to your `config.js` file:

```javascript
module.exports = {
	// ... other configuration

	scriptHooks: {
		prefetch: "npm run lint",
		postfetch: 'echo "Content fetched successfully"',
		prebuild: "npm run test",
		postbuild: "rsync -av build/ user@server:/var/www/",
		prewatch: "npm run setup-dev",
		preassets: "npm run optimize-images",
		postassets: "aws s3 sync assets/ s3://my-bucket/assets/",
	},
};
```

### Hook Execution

- Hooks are executed in the project root directory
- If a hook fails, a warning is displayed but the main command continues
- Hooks support any shell command or script
- Environment variables are available to hook scripts

### Examples

**Linting before builds:**

```javascript
scriptHooks: {
	prebuild: "npm run lint && npm run format";
}
```

**Database sync before fetching:**

```javascript
scriptHooks: {
	prefetch: "node scripts/sync-database.js";
}
```

**Deploy after build:**

```javascript
scriptHooks: {
	postbuild: "npm run deploy";
}
```

## Advanced Features

### Critical CSS

Flash 2024 can generate critical CSS for improved page load performance:

```bash
flash critical
```

### Asset Optimization

Assets are automatically optimized during the build process:

- SCSS is compiled to CSS and minified
- JavaScript is minified
- Images are optimized

### Custom Plugins

You can create custom plugins by adding them to the `plugins` directory:

```javascript
// plugins/myPlugin.js
module.exports = function (parameter) {
	// Your plugin code here
	return result;
};
```
